import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import {resolve} from 'path'
import {libConfig} from "./util/libConfig.ts";


// https://vite.dev/config/
export default defineConfig({
    plugins: [
        vue(),
    ],
    resolve: {
        alias: {
            '@': resolve(__dirname, './src'),
        },
    },
    build: {
        emptyOutDir: false,
        lib: libConfig,
        rollupOptions: {
            external: ['vue', 'ant-design-vue', 'vue-router', 'mstf-kit', 'ali-oss'],
            output: {
                globals: {
                    vue: 'Vue',
                    "ant-design-vue": "AntDesignVue",
                    "vue-router": 'VurRouter',
                    "mstf-kit": "MstfKit",
                    "ali-oss": "AliOss"
                }
            },
        },
        terserOptions: {
            compress: {
                drop_console: true,
                drop_debugger: true,
            }
        }
    },
    esbuild: {
        drop: ['debugger'],
    },
    server: {
        proxy: {
            // 代理所有对 localhost:5173/debug/ 的请求到 5173
            '/debug': {
                target: 'https://devchat.ai.izdax.cn',  // 目标地址
                changeOrigin: true,  // 是否修改请求头中的 Origin 字段
                rewrite: (path) => path.replace(/^\/debug/, '/debug/'), // 代理时将 /debug 替换为空
            },
        },
    },
})
