import type {UserInfo} from "@/types/user.ts";
import type {GlobalUtilsFun, MicVadFunction} from "@/types/vid";

interface MultipartUploadFileParams {
    obj: string;
    file: Blob | File;
    onProgress?: (percentCompleted: number) => void;
}

export interface IOSSUploader {
    uploadFile({
                   obj,
                   file,
                   onProgress,
               }: MultipartUploadFileParams): Promise<UploadResponse>;
}


// 定义utilsStore的types类型
export interface UtilsStores {
    translateFontSize: number;
    setTranslateFontSize: (value: number) => void;
    setCustomVariable: (key:string, value: any) => void;
    getCustomVariable: (key:string) => void;
}

// 用户信息store信息
export interface UserStores {
    userInfo: UserInfo
}

// 定义Global类型
export interface GlobalUtils {
    vad: GlobalUtilsFun
}

export interface ChunkChat {
    delta: DeltaContent;
    finish_reason: 'stop' | 'length' | 'tool_calls' | 'content_filter' | 'function_call' | null;
    index: number;
}

export interface DeltaContent {
    content?: string | null;
    function_call?: Delta.FunctionCall;
    refusal?: string | null;
    role?: 'developer' | 'system' | 'user' | 'assistant' | 'tool';
    tool_calls?: Array<Delta.ToolCall>;
}


// 定义openaiChat格式
export interface OpenAiChat {
    openAiChatCustom: {
        getClient(baseURL?: string): void

        startStream(signal: AbortSignal, params: {
            message: string,
            baseURL: string,
            merge: boolean,
        }, onMessage: (chat: {
            choices:ChunkChat[]
        }) => void): Promise<boolean>;
    }

}


