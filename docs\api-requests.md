# API请求封装指南

## 一、基础API请求

系统提供统一的API请求接口，在plugins目录中server文件夹进行封装使用：

```typescript
// server/server.ts
import {getInject} from "@/utils/getInject";

export const demoPluginSerApi = () => {
    const {api} = getInject(); // 可以获取各种工具

    const create = async () => {
        return await api.request.main.get('test');
    };

    return {
        create,
    };
};

// 在组件中使用
import {demoPluginSerApi} from './server/server.ts';

const demoApi = demoPluginSerApi();

const fetchData = async () => {
    const result = await demoApi.create();
    console.log(result);
};
```

## 二、API接口规范

```typescript
interface Api {
    request: {
        ai: ApiService;
        main: ApiService;
        agent: ApiService;
        video: ApiService;
        module: ApiService;
        client: ApiService;
    },
    streamFetchRequest: {
        ai: StreamFetchService;
        main: StreamFetchService;
        agent: StreamFetchService;
        video: StreamFetchService;
        module: StreamFetchService;
        client: StreamFetchService;
    }
}

// ApiService接口
interface ApiService {
    get<T>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T>;

    post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;

    put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;

    delete<T>(url: string, options?: {
        params?: any,
        data?: any,
        config?: AxiosRequestConfig
    }): Promise<T>;
}
```

## 三、流式请求支持

系统提供了基于Fetch API的流式请求支持，适用于SSE等流式通信场景，可通过以下方式使用：

```typescript
// server/server.ts 中使用
export const demoPluginSerApi = () => {
    const {api} = getInject();

    // 发送流式请求并处理响应
    const streamRequest = async (prompt: string) => {
        try {
            // 获取流式响应
            const response = await api.streamFetchRequest.main.stream('/api/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({prompt}),
                baseURL: 'https://api.example.com', // 基础URL，最终请求地址将为https://api.example.com/api/stream
                params: {version: '1.0'},         // 查询参数，自动转为?version=1.0附加到URL
                timeout: 30000                       // 超时时间30秒，超时后自动中断请求
            });

            // 确保响应状态正常
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 获取响应流
            const reader = response.body?.getReader();
            if (!reader) {
                throw new Error('无法获取响应流');
            }

            return reader; // 返回reader供组件处理
        } catch (error) {
            console.error('流式请求错误:', error);
            throw error;
        }
    };

    // 普通GET请求
    const normalGet = async <T>(path: string): Promise<T> => {
        return await api.streamFetchRequest.main.get<T>(path, {
            baseURL: 'https://api.example.com', // 基础URL，与path拼接
            params: {lang: 'zh-CN'},         // 查询参数，转为?lang=zh-CN
            timeout: 10000                      // 10秒超时
        });
    };

    // 普通POST请求
    const normalPost = async <T>(path: string, data: any): Promise<T> => {
        return await api.streamFetchRequest.main.post<T>(path, data, {
            baseURL: 'https://api.example.com', // 基础URL
            timeout: 15000                      // 15秒超时
        });
    };

    return {
        streamRequest,
        normalGet,
        normalPost
    };
};
```

### 在组件中消费流式响应

```vue
<script setup>
  import {ref, onMounted} from 'vue';
  import {demoPluginSerApi} from '../server/server';

  const api = demoPluginSerApi();
  const responseText = ref('');
  const isLoading = ref(false);

  const fetchStreamData = async () => {
    isLoading.value = true;
    responseText.value = '';

    try {
      const reader = await api.streamRequest('您的问题是什么?');

      // 处理流式响应
      const decoder = new TextDecoder();
      let done = false;

      while (!done) {
        const {value, done: readerDone} = await reader.read();
        done = readerDone;

        if (value) {
          const text = decoder.decode(value, {stream: !done});

          // 处理SSE格式数据 (data: xxx\n\n)
          const lines = text.split('\n\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6); // 移除 'data: ' 前缀
              if (data !== '[DONE]') {
                try {
                  const parsedData = JSON.parse(data);
                  // 根据API返回格式处理数据
                  responseText.value += parsedData.content || '';
                } catch (e) {
                  // 纯文本格式
                  responseText.value += data;
                }
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('流处理错误:', error);
    } finally {
      isLoading.value = false;
    }
  };
</script>

<template>
  <div>
    <button @click="fetchStreamData" :disabled="isLoading">
      {{ isLoading ? '加载中...' : '发送流式请求' }}
    </button>

    <div class="response-container">
      <pre>{{ responseText }}</pre>
    </div>
  </div>
</template>
```

### 流式请求接口类型定义

```typescript
export interface FetchOptions extends RequestInit {
    baseURL?: string;    // 基础URL，会与请求路径拼接
    params?: Record<string, any>;  // URL查询参数，会自动转换为?key1=value1&key2=value2格式
    timeout?: number;    // 请求超时时间，单位毫秒
}

export interface StreamFetchService {
    /**
     * 发送流式请求，直接返回Response对象
     * 用户需要自行处理Response.body
     */
    stream(url: string, options?: FetchOptions): Promise<Response>;

    /**
     * 发送普通POST请求
     */
    post<T>(url: string, data?: any, options?: FetchOptions): Promise<T>;

    /**
     * 发送普通GET请求
     */
    get<T>(url: string, options?: FetchOptions): Promise<T>;

    /**
     * 发送文件上传请求，自动处理FormData和Content-Type
     * 不会手动设置Content-Type，而是让浏览器根据FormData自动处理
     */
    upload(url: string, formData: FormData, options?: FetchOptions): Promise<Response>;
}
```

## 四、文件上传请求

系统提供了专门的文件上传API，特点是自动处理FormData的Content-Type，避免在上传文件时常见的边界问题：

```typescript
// server/server.ts 中使用
export const fileUploadService = () => {
    const {api} = getInject();

    // 使用专用的upload方法上传文件
    const uploadFile = async (file: File, extraData?: Record<string, string>) => {
        try {
            // 创建FormData对象
            const formData = new FormData();
            formData.append('file', file); // 添加文件

            // 添加其他表单数据
            if (extraData) {
                Object.entries(extraData).forEach(([key, value]) => {
                    formData.append(key, value);
                });
            }

            // 使用upload方法，自动处理Content-Type
            const response = await api.streamFetchRequest.client.upload('/api/upload', formData, {
                // 可选配置项
                timeout: 60000, // 上传超时时间
                baseURL: 'https://api.example.com', // 基础URL
                // 其他Fetch选项，但无需手动设置Content-Type
            });

            if (!response.ok) {
                throw new Error(`上传失败: ${response.status}`);
            }

            // 返回响应数据
            return await response.json();
        } catch (error) {
            console.error('文件上传错误:', error);
            throw error;
        }
    };

    return {
        uploadFile
    };
};
```

### 在组件中使用文件上传

```vue
<template>
  <div>
    <input type="file" @change="handleFileChange">
    <button @click="uploadSelectedFile" :disabled="!selectedFile || isUploading">
      {{ isUploading ? '上传中...' : '上传文件' }}
    </button>
    <div v-if="uploadResult">
      上传成功: {{ uploadResult.fileUrl }}
    </div>
  </div>
</template>

<script setup>
  import {ref} from 'vue';
  import {fileUploadService} from '../server/server';

  const api = fileUploadService();
  const selectedFile = ref(null);
  const isUploading = ref(false);
  const uploadResult = ref(null);

  const handleFileChange = (event) => {
    selectedFile.value = event.target.files[0] || null;
  };

  const uploadSelectedFile = async () => {
    if (!selectedFile.value) return;

    isUploading.value = true;
    uploadResult.value = null;

    try {
      // 调用上传方法
      const result = await api.uploadFile(selectedFile.value, {
        fileName: selectedFile.value.name,
        fileType: selectedFile.value.type,
        userId: '12345'
      });

      uploadResult.value = result;
      selectedFile.value = null; // 清空已选文件
    } catch (error) {
      console.error('上传失败:', error);
      alert('文件上传失败，请重试');
    } finally {
      isUploading.value = false;
    }
  };
</script>
```

> 注意：upload方法会自动让浏览器处理FormData的Content-Type，并设置正确的boundary参数，这对文件上传至关重要。不要手动设置Content-Type，否则会导致文件上传失败。