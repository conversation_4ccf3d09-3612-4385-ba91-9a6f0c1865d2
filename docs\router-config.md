# 路由配置指南

## 插件路由配置

插件系统提供自动路由注册功能，在router.ts中配置：

```typescript
import {type RouteRecordRaw} from 'vue-router';
import {routers} from "@/utils/routerHelper.ts";

const demoPluginRoutes: RouteRecordRaw[] = [
    {
        path: '/demo-plugin',
        name: 'DemoPlugin',
        component: () => import('./index.vue'),
        meta: {
            title: 'Demo Plugin',
            requiresAuth: false,
            ivViewMenu: false
        }
    }
];

let isRoutesAdded = false;

const initModule = () => {
    if (import.meta.env.MODE === 'production' && !isRoutesAdded) {
        const router = routers();
        demoPluginRoutes.forEach(route => {
            router.addRoute("Layout", route);
        });
        isRoutesAdded = true;
    }
};

export {
    demoPluginRoutes as default,
    initModule
};
```

## 路由守卫

系统提供了全局路由守卫，会自动处理：

- 登录状态验证
- 页面标题设置
- 权限检查



## 路由参数

### 动态路由参数

```typescript
const dynamicRoutes: RouteRecordRaw[] = [
    {
        path: '/user/:id',
        name: 'UserDetail',
        component: () => import('./views/UserDetail.vue'),
        meta: {
            title: '用户详情',
            requiresAuth: true
        }
    }
];
```

## 编程式导航

```typescript
import { routers } from "@/utils/routerHelper.ts";

const router = routers();

// 跳转到指定路由
router.push('/dashboard');

// 带参数跳转
router.push({
    name: 'UserDetail',
    params: { id: '123' }
});

// 带查询参数跳转
router.push({
    path: '/search',
    query: { keyword: 'vue' }
});

// 替换当前路由
router.replace('/login');

// 返回上一页
router.back();

// 前进到下一页
router.forward();
```

## 最佳实践

1. **路由命名规范**：使用PascalCase命名路由
2. **路径规范**：使用kebab-case命名路径
3. **懒加载**：使用动态import实现路由组件懒加载
4. **权限控制**：合理使用requiresAuth字段
5. **SEO优化**：为每个路由设置合适的title