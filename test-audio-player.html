<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AudioPlayer 响应式测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .device-simulator {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            background: white;
        }
        
        .device-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .desktop { width: 100%; }
        .tablet { width: 768px; max-width: 100%; }
        .mobile { width: 480px; max-width: 100%; }
        .small-mobile { width: 320px; max-width: 100%; }
        
        /* 模拟 AudioPlayer 样式 */
        .audio-player {
            width: 100%;
        }
        
        .player-content {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            height: 48px;
            background: #edf6ff21;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border: 1px solid #e0e0e0;
        }
        
        .play-toggle {
            flex-shrink: 0;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .line-progress {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
            overflow: hidden;
        }
        
        .line-item {
            width: 3px;
            height: 16px;
            background: #bfdffc;
            margin: 1px;
            border-radius: 6px;
            flex-shrink: 0;
        }
        
        .time {
            font-size: 12px;
            color: #666;
            margin-left: 8px;
            white-space: nowrap;
            min-width: 70px;
            text-align: center;
            flex-shrink: 0;
        }
        
        .other-controller {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 10px;
        }
        
        .control-btn {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 响应式样式 */
        @media (max-width: 1200px) {
            .player-content {
                width: 95%;
            }
        }

        @media (max-width: 768px) {
            .player-content {
                width: 98%;
                height: 44px;
            }
            
            .play-toggle {
                width: 24px;
                height: 24px;
                margin: 0 8px;
            }
            
            .line-item {
                width: 2px;
                margin: 0.5px;
            }
            
            .time {
                font-size: 11px;
                min-width: 60px;
                margin-left: 6px;
            }
            
            .other-controller {
                margin: 0 8px;
                gap: 6px;
            }
            
            .control-btn {
                width: 20px;
                height: 20px;
            }
        }
        
        @media (max-width: 480px) {
            .player-content {
                width: 100%;
                height: 40px;
            }
            
            .play-toggle {
                width: 20px;
                height: 20px;
                margin: 0 6px;
            }
            
            .line-item {
                width: 1.5px;
                margin: 0.3px;
            }
            
            .time {
                font-size: 10px;
                min-width: 50px;
                margin-left: 4px;
            }
            
            .other-controller {
                margin: 0 6px;
                gap: 4px;
            }
            
            .control-btn {
                width: 18px;
                height: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AudioPlayer 响应式测试</h1>

        <div class="test-section">
            <h2>大屏桌面端 (1200px+) - 最大宽度800px</h2>
            <div class="device-simulator desktop">
                <div class="device-title">大屏桌面端显示效果 - 播放器宽度被限制在800px以内，居中显示</div>
                <div class="audio-player">
                    <div class="player-content">
                        <div class="play-toggle">▶</div>
                        <div class="line-progress">
                            <!-- 生成100个线条 -->
                            <script>
                                for(let i = 0; i < 100; i++) {
                                    document.write('<div class="line-item" style="height: ' + (8 + Math.random() * 20) + 'px;"></div>');
                                }
                            </script>
                            <div class="time">01:23 / 03:45</div>
                        </div>
                        <div class="other-controller">
                            <div class="control-btn">↻</div>
                            <div class="control-btn">↓</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>中等屏幕 (768px-1200px) - 宽度95%</h2>
            <div class="device-simulator tablet">
                <div class="device-title">中等屏幕显示效果 - 使用95%宽度</div>
                <div class="audio-player">
                    <div class="player-content">
                        <div class="play-toggle">▶</div>
                        <div class="line-progress">
                            <!-- 生成80个线条 -->
                            <script>
                                for(let i = 0; i < 80; i++) {
                                    document.write('<div class="line-item" style="height: ' + (8 + Math.random() * 20) + 'px;"></div>');
                                }
                            </script>
                            <div class="time">01:23 / 03:45</div>
                        </div>
                        <div class="other-controller">
                            <div class="control-btn">↻</div>
                            <div class="control-btn">↓</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>手机端 (480px)</h2>
            <div class="device-simulator mobile">
                <div class="device-title">手机端显示效果</div>
                <div class="audio-player">
                    <div class="player-content">
                        <div class="play-toggle">▶</div>
                        <div class="line-progress">
                            <!-- 生成60个线条 -->
                            <script>
                                for(let i = 0; i < 60; i++) {
                                    document.write('<div class="line-item" style="height: ' + (8 + Math.random() * 20) + 'px;"></div>');
                                }
                            </script>
                            <div class="time">01:23 / 03:45</div>
                        </div>
                        <div class="other-controller">
                            <div class="control-btn">↻</div>
                            <div class="control-btn">↓</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>小屏手机 (320px)</h2>
            <div class="device-simulator small-mobile">
                <div class="device-title">小屏手机显示效果</div>
                <div class="audio-player">
                    <div class="player-content">
                        <div class="play-toggle">▶</div>
                        <div class="line-progress">
                            <!-- 生成40个线条 -->
                            <script>
                                for(let i = 0; i < 40; i++) {
                                    document.write('<div class="line-item" style="height: ' + (8 + Math.random() * 20) + 'px;"></div>');
                                }
                            </script>
                            <div class="time">01:23 / 03:45</div>
                        </div>
                        <div class="other-controller">
                            <div class="control-btn">↻</div>
                            <div class="control-btn">↓</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加窗口大小变化提示
        window.addEventListener('resize', function() {
            console.log('窗口宽度:', window.innerWidth);
        });
    </script>
</body>
</html>
