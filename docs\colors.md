# 主题颜色配置

## 暗色模式颜色

| 变量名                    | 颜色值                         | 颜色显示                                                                                     |
|------------------------|-----------------------------|------------------------------------------------------------------------------------------|
| `$dark-bg`             | `#141414`                   | <div style="background-color:#141414; width:50px; height:20px;"></div>                   |
| `$dark-component`      | `#1f1f1f`                   | <div style="background-color:#1f1f1f; width:50px; height:20px;"></div>                   |
| `$dark-text`           | `rgba(255, 255, 255, 0.85)` | <div style="background-color:rgba(255, 255, 255, 0.85); width:50px; height:20px;"></div> |
| `$dark-text-secondary` | `rgba(255, 255, 255, 0.45)` | <div style="background-color:rgba(255, 255, 255, 0.45); width:50px; height:20px;"></div> |
| `$dark-border`         | `#303030`                   | <div style="background-color:#303030; width:50px; height:20px;"></div>                   |
| `$dark-primary`        | `#177ddc`                   | <div style="background-color:#177ddc; width:50px; height:20px;"></div>                   |
| `$dark-primary-hover`  | `rgba(23, 125, 220, 0.15)`  | <div style="background-color:rgba(23, 125, 220, 0.15); width:50px; height:20px;"></div>  |

## 亮色模式颜色

| 变量名              | 颜色值                       | 颜色显示                                                                                   |
|------------------|---------------------------|----------------------------------------------------------------------------------------|
| `$bg-base`       | `#fff`                    | <div style="background-color:#fff; width:50px; height:20px;"></div>                    |
| `$text-primary`  | `#1f1f1f`                 | <div style="background-color:#1f1f1f; width:50px; height:20px;"></div>                 |
| `$primary-color` | `#1890ff`                 | <div style="background-color:#1890ff; width:50px; height:20px;"></div>                 |
| `$primary-hover` | `rgba(24, 144, 255, 0.1)` | <div style="background-color:rgba(24, 144, 255, 0.1); width:50px; height:20px;"></div> |

## 渐变色

| 变量名                  | 颜色值                                                 | 颜色显示                                                                                                        |
|----------------------|-----------------------------------------------------|-------------------------------------------------------------------------------------------------------------|
| `$gradient-primary`  | `linear-gradient(135deg, #1890ff 0%, #36cfc9 100%)` | <div style="background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%); width:50px; height:20px;"></div> |
| `$gradient-disabled` | `linear-gradient(135deg, #bfbfbf 0%, #d9d9d9 100%)` | <div style="background: linear-gradient(135deg, #bfbfbf 0%, #d9d9d9 100%); width:50px; height:20px;"></div> |

## VIP相关颜色

| 变量名             | 颜色值                                                | 颜色显示                                                                                                       |
|-----------------|----------------------------------------------------|------------------------------------------------------------------------------------------------------------|
| `$vip-gold`     | `#ffd666`                                          | <div style="background-color:#ffd666; width:50px; height:20px;"></div>                                     |
| `$vip-gradient` | `linear-gradient(90deg, #ffd666 0%, #ffc53d 100%)` | <div style="background: linear-gradient(90deg, #ffd666 0%, #ffc53d 100%); width:50px; height:20px;"></div> |

## 圆角和间距

| 变量名           | 值      | 描述   |
|---------------|--------|------|
| `$radius-sm`  | `4px`  | 小圆角  |
| `$radius-md`  | `8px`  | 中等圆角 |
| `$radius-lg`  | `12px` | 大圆角  |
| `$spacing-xs` | `4px`  | 小间距  |
| `$spacing-sm` | `8px`  | 中小间距 |
| `$spacing-md` | `16px` | 中间距  |
| `$spacing-lg` | `24px` | 大间距  |
| `$spacing-xl` | `32px` | 超大间距 |

## 过渡动画

```scss
$transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
```

## 主题切换相关样式

在以下的代码中，我们根据不同的主题（亮色或暗色）设置了相关的颜色和样式。

### 亮色主题

```scss
:root {
  --bg-color: #{$bg-base};
  --text-color: #{$text-primary};
  --border-color: #f0f0f0;
  --primary-color: #{$primary-color};
  --hover-bg: #{$primary-hover};
}
```

### 暗色主题

```scss
:root.dark {
  --bg-color: #{$dark-bg};
  --text-color: #{$dark-text};
  --border-color: #{$dark-border};
  --primary-color: #{$dark-primary};
  --hover-bg: #{$dark-primary-hover};
}
```

### 说明

- izdaxAi官网颜色匹配
- 支持亮色/暗色主题切换
- 提供完整的颜色变量系统
- 包含实用的颜色工具函数