// libConfig.ts
import {resolve, join} from 'path';
import {LibraryOptions} from "vite";

const pluginName = process.env.PLUGIN_NAME || 'default_value';
// 获取当前模块的目录
const projectRootDir = resolve(process.cwd());  // 使用 process.cwd() 获取项目根目录

export const libConfig: LibraryOptions = {
    entry: join(projectRootDir, `src/plugins/${pluginName}/index.ts`),
    name: 'izdaxPlugin',
    formats: ['umd'],
    fileName: (format: string) => `${pluginName}/izdax-plugin-${pluginName}.${format}.js`,
    cssFileName: `${pluginName}/izdax-plugin-${pluginName}`,
};
