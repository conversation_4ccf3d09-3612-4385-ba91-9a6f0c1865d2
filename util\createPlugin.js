const _0x53228c=_0x5f41;function _0xda42(){const _0x12da39=['slice','SerApi()\x0aconsole.log(','19134XHXAqS','906437aQtTKt','//\x20这是\x20','\x20插件\x27,\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20requiresAuth:\x20false,\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20ivViewMenu:\x20false\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20}\x0a];\x0a\x0alet\x20isRoutesAdded\x20=\x20false;\x0a\x0aconst\x20initModule\x20=\x20()\x20=>\x20{\x0a\x20\x20\x20\x20if\x20(import.meta.env.MODE\x20===\x20\x27production\x27\x20&&\x20!isRoutesAdded)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20const\x20router\x20=\x20routers();\x0a\x20\x20\x20\x20\x20\x20\x20\x20','文件已创建:\x20','目录已创建:\x20','26PTKWtl','server.ts','5qljNma','plugins','join','\x20插件的导出文件\x0aimport\x20','\x20as\x20default,\x0a\x20\x20//\x20','mkdirSync','test','项目根目录：','Routes:\x20RouteRecordRaw[]\x20=\x20[\x0a\x20\x20\x20\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20path:\x20\x27/','11FnctZp','\x20插件</h1>\x0a\x20\x20</div>\x0a</template>\x0a\x0a<style\x20lang=\x22scss\x22\x20scoped>\x0a</style>\x0a','10lZarUf','components','<script\x20lang=\x27ts\x27\x20setup>\x0aimport\x20{initModule}\x20from\x20\x27./router.ts\x27\x0aimport\x20{onMounted}\x20from\x20\x27vue\x27;\x0aimport\x20{','assets','resolve','log','\x27,\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20component:\x20()=>\x20import(\x27./index.vue\x27),\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20meta:\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20title:\x20\x27','exit','import\x20{type\x20RouteRecordRaw}\x20from\x20\x27vue-router\x27;\x0aimport\x20{routers}\x20from\x20\x22@/utils/routerHelper.ts\x22;\x0a\x0aconst\x20','toUpperCase','server','177676oGhKHR','Routes\x20as\x20router\x20\x20多路由创建使用\x0a};\x0a','577884FfWccQ','charAt','SerApi\x20=\x20()\x20=>\x20{\x0a\x20\x20\x20\x20const\x20{api}\x20=\x20getInject();\x20//\x20可以括号里获取封装好的方法数据\x20router\x20/\x20ossUploader\x20等\x0a\x0a\x20\x20\x20\x20const\x20create\x20=\x20async\x20()\x20=>\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20return\x20await\x20api.request.main.get(\x27test\x27);\x0a\x20\x20\x20\x20};\x0a\x0a\x20\x20\x20\x20return\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20create,\x0a\x20\x20\x20\x20};\x0a};\x0a','src','existsSync','index.ts','3724767sYgvmk','cwd','import\x20{getInject}\x20from\x20\x22@/utils/getInject.ts\x22;\x0a\x0aexport\x20const\x20','router.ts','32qMeFHk','961764HdczZh','writeFileSync','2177370NtcqxU','\x20from\x20\x27./index.vue\x27;\x0a//\x20import\x20'];_0xda42=function(){return _0x12da39;};return _0xda42();}(function(_0x19c9bb,_0x29d07f){const _0x87c2db=_0x5f41,_0x13fa75=_0x19c9bb();while(!![]){try{const _0xd742a=parseInt(_0x87c2db(0x16c))/0x1*(parseInt(_0x87c2db(0x166))/0x2)+parseInt(_0x87c2db(0x160))/0x3+parseInt(_0x87c2db(0x153))/0x4*(parseInt(_0x87c2db(0x16e))/0x5)+-parseInt(_0x87c2db(0x162))/0x6+parseInt(_0x87c2db(0x167))/0x7*(parseInt(_0x87c2db(0x15f))/0x8)+parseInt(_0x87c2db(0x15b))/0x9*(-parseInt(_0x87c2db(0x148))/0xa)+-parseInt(_0x87c2db(0x146))/0xb*(-parseInt(_0x87c2db(0x155))/0xc);if(_0xd742a===_0x29d07f)break;else _0x13fa75['push'](_0x13fa75['shift']());}catch(_0x1a75c4){_0x13fa75['push'](_0x13fa75['shift']());}}}(_0xda42,0x626a8));import _0x3606a7 from'fs';import _0x7ee744 from'path';const pluginName=process['argv'][0x2],projectRootDir=_0x7ee744[_0x53228c(0x14c)](process[_0x53228c(0x15c)]());(!pluginName||!/^[a-zA-Z0-9]+$/[_0x53228c(0x143)](pluginName))&&(console['error']('错误：插件名称只能包含英文字母和数字，且不能为空！'),process[_0x53228c(0x14f)](0x1));console[_0x53228c(0x14d)](_0x53228c(0x144),projectRootDir);const pluginDir=_0x7ee744[_0x53228c(0x170)](projectRootDir,_0x53228c(0x158),_0x53228c(0x16f),pluginName),pluginComponentsDir=_0x7ee744[_0x53228c(0x170)](pluginDir,_0x53228c(0x149)),pluginServerDir=_0x7ee744['join'](pluginDir,_0x53228c(0x152)),pluginViewDir=_0x7ee744[_0x53228c(0x170)](pluginDir,'view'),pluginAssetsDir=_0x7ee744[_0x53228c(0x170)](pluginDir,_0x53228c(0x14b));!_0x3606a7[_0x53228c(0x159)](pluginDir)&&(_0x3606a7[_0x53228c(0x142)](pluginDir,{'recursive':!![]}),console[_0x53228c(0x14d)](_0x53228c(0x16b)+pluginDir));!_0x3606a7[_0x53228c(0x159)](pluginComponentsDir)&&(_0x3606a7['mkdirSync'](pluginComponentsDir,{'recursive':!![]}),console['log'](_0x53228c(0x16b)+pluginComponentsDir));!_0x3606a7['existsSync'](pluginServerDir)&&(_0x3606a7[_0x53228c(0x142)](pluginServerDir,{'recursive':!![]}),console['log'](_0x53228c(0x16b)+pluginServerDir));!_0x3606a7[_0x53228c(0x159)](pluginViewDir)&&(_0x3606a7[_0x53228c(0x142)](pluginViewDir,{'recursive':!![]}),console[_0x53228c(0x14d)](_0x53228c(0x16b)+pluginViewDir));!_0x3606a7[_0x53228c(0x159)](pluginAssetsDir)&&(_0x3606a7['mkdirSync'](pluginViewDir,{'recursive':!![]}),console[_0x53228c(0x14d)]('目录已创建:\x20'+pluginViewDir));const vueContent=_0x53228c(0x14a)+pluginName+'SerApi}\x20from\x20\x27./server/server.ts\x27\x20//api使用方式\x0aimport\x20{routers}\x20from\x20\x22@/utils/routerHelper.ts\x22;\x20//\x20路由使用方式\x0a\x0aconst\x20'+pluginName+'Api\x20=\x20'+pluginName+_0x53228c(0x165)+pluginName+'Api)\x0aconsole.log(routers)\x0a\x0aonMounted(()\x20=>\x20{\x0a\x20\x20//\x20系统级部分\x20误删！\x0a\x20\x20initModule();\x0a})\x0a</script>\x0a\x0a<template>\x0a\x20\x20<div>\x0a\x20\x20\x20\x20<h1>'+pluginName+_0x53228c(0x147),vuePath=_0x7ee744[_0x53228c(0x170)](pluginDir,'index.vue');_0x3606a7[_0x53228c(0x161)](vuePath,vueContent),console[_0x53228c(0x14d)](_0x53228c(0x16a)+vuePath);let routerTsContent=_0x53228c(0x150)+pluginName+_0x53228c(0x145)+pluginName+'\x27,\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20name:\x20\x27'+(pluginName[_0x53228c(0x156)](0x0)[_0x53228c(0x151)]()+pluginName[_0x53228c(0x164)](0x1))+_0x53228c(0x14e)+pluginName+_0x53228c(0x169)+pluginName+'Routes.forEach(route\x20=>\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20router.addRoute(\x22Layout\x22,\x20route);\x0a\x20\x20\x20\x20\x20\x20\x20\x20});\x0a\x20\x20\x20\x20\x20\x20\x20\x20isRoutesAdded\x20=\x20true;\x0a\x20\x20\x20\x20}\x0a};\x0a\x0aexport\x20{\x0a\x20\x20\x20\x20'+pluginName+'Routes\x20as\x20default,\x0a\x20\x20\x20\x20initModule\x0a};\x0a';const routerTsPath=_0x7ee744['join'](pluginDir,_0x53228c(0x15e));_0x3606a7['writeFileSync'](routerTsPath,routerTsContent),console[_0x53228c(0x14d)](_0x53228c(0x16a)+routerTsPath);let serverTsContent=_0x53228c(0x15d)+pluginName+_0x53228c(0x157);function _0x5f41(_0x52c71a,_0x35080d){const _0xda4241=_0xda42();return _0x5f41=function(_0x5f4112,_0x545594){_0x5f4112=_0x5f4112-0x140;let _0x452db8=_0xda4241[_0x5f4112];return _0x452db8;},_0x5f41(_0x52c71a,_0x35080d);}const serverTsPath=_0x7ee744['join'](pluginServerDir,_0x53228c(0x16d));_0x3606a7[_0x53228c(0x161)](serverTsPath,serverTsContent),console[_0x53228c(0x14d)](_0x53228c(0x16a)+serverTsPath);const pluginFileName=pluginName['charAt'](0x0)[_0x53228c(0x151)]()+pluginName[_0x53228c(0x164)](0x1),indexTsContent=_0x53228c(0x168)+pluginName+_0x53228c(0x140)+pluginFileName+_0x53228c(0x163)+pluginName+'Routes\x20from\x20\x27./router\x27;\x20多路由创建使用\x0a\x0aexport\x20{\x0a\x20\x20'+pluginFileName+_0x53228c(0x141)+pluginName+_0x53228c(0x154),indexTsPath=_0x7ee744['join'](pluginDir,_0x53228c(0x15a));_0x3606a7[_0x53228c(0x161)](indexTsPath,indexTsContent),console[_0x53228c(0x14d)]('文件已创建:\x20'+indexTsPath);