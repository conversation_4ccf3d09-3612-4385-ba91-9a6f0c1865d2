/**
 * 语音活动检测（VAD）配置选项
 */
export interface VADOptions {
    /**
     * 基础资源路径
     */
    baseAssetPath?: string;

    /**
     * ONNX WASM 基础路径
     */
    onnxWASMBasePath?: string;

    /**
     * 语音结束回调函数
     * @param audio 检测到的音频数据
     */
    onSpeechEnd?: (audio: Float32Array) => void;

    /**
     * 语音开始回调函数
     */
    onSpeechStart?: () => void;

    /**
     * 错误回调函数
     * @param error 错误信息
     */
    onError?: (error: Error) => void;
}

/**
 * VAD 实例接口
 */
export interface VADInstance {
    /**
     * 开始语音检测
     */
    start: () => void;

    /**
     * 暂停语音检测
     */
    pause: () => void;

    /**
     * 销毁 VAD 实例，释放资源
     */
    destroy: () => void;

    /**
     * 当前 VAD 状态
     */
    state: 'started' | 'paused' | 'destroyed';
}

export interface SpeechProbabilities {
    notSpeech: number;
    isSpeech: number;
}

/**
 * MicVAD 函数接口
 * 初始化语音活动检测并返回控制实例
 *
 * @param onSpeechEndCallback 语音结束时的回调函数，接收转换后的音频 Blob 和格式
 * @param format 音频输出格式，默认为 WAV
 * @returns VAD 实例，用于控制语音检测
 */
export interface MicVadFunction {
    (
        onSpeechStartCallback:()=>void,
        onFrameProcessedCallback?: (probabilities: SpeechProbabilities, frame: Float32Array) => void,
        onSpeechEndCallback: (result: { blob: Blob, format: AudioFormat, duration: number }) => void,
        format?: AudioFormat
    ): Promise<VADInstance | undefined>;
}


export declare class MicVAD {
    options: RealTimeVADOptions;
    private audioContext;
    private stream;
    private audioNodeVAD;
    private sourceNode;
    private listening;
    static new(options?: Partial<RealTimeVADOptions>): Promise<MicVAD>;
    private constructor();
    pause: () => void;
    start: () => void;
    destroy: () => void;
}

/**
 * 全局工具接口
 */
export interface GlobalUtilsFun {
    /**
     * 语音活动检测初始化函数
     */
    init: MicVadFunction;
    getVadInstance():VADInstance

    // 可以添加其他全局工具函数
}
