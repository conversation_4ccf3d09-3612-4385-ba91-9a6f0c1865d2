import {useProvideInject} from "@/utils/routerHelper.ts";
import type {Api} from "@/types/api.ts";
import type {Router} from "vue-router";
import type {GlobalUtils, IOSSUploader, OpenAiChat, UserStores, UtilsStores} from "@/types/global";

export const getInject = () => {
    return {
        api: useProvideInject<Api>('api'),
        router: useProvideInject<Router>("router"),
        userStore: useProvideInject<UserStores>("userStore"),
        ossUtils: useProvideInject<IOSSUploader>('ossUtils'),
        utilStore: useProvideInject<UtilsStores>('utilStore'),
        globalUtils: useProvideInject<GlobalUtils>('globalUtils'),
        openChat: useProvideInject<OpenAiChat>('openAiChatCustom'),
        getEnv: useProvideInject<'development' | 'production'>('getEnv'),
    }
}