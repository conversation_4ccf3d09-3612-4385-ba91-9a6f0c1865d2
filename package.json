{"name": "izdax-ai-plugin-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "preview": "vite preview", "run-build": "node util/run-build.js", "create-plugin": "node util/createPlugin.js"}, "dependencies": {"ali-oss": "^6.22.0", "ant-design-vue": "^4.2.6", "axios": "^1.7.9", "mstf-kit": "^0.4.8", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/ali-oss": "^6.16.11", "@types/node": "^22.13.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "sass": "^1.84.0", "typescript": "~5.6.2", "vite": "6.0.9", "vue-tsc": "^2.2.0"}}