// @/types/api.ts
import type {AxiosRequestConfig} from "axios";

export interface ApiService {
    get<T>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T>;

    post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;

    put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;

    delete<T>(url: string, options?: {
        params?: any,  // 可选的查询参数
        data?: any,    // 可选的请求体数据
        config?: AxiosRequestConfig  // 可选的配置对象，默认值为 AxiosRequestConfig 类型
    }): Promise<T>;
}

export interface FetchOptions extends RequestInit {
    baseURL?: string;
    params?: Record<string, any>;
    timeout?: number;
}

export interface StreamFetchService {
    /**
     * 发送流式请求，直接返回Response对象
     * 用户需要自行处理Response.body
     */
    stream(url: string, options?: FetchOptions): Promise<Response>;

    /**
     * 发送普通POST请求
     */
    post(url: string, data?: any, options?: FetchOptions): Promise<Response>;

    /**
     * 发送普通GET请求
     */
    get(url: string, options?: FetchOptions): Promise<Response>;
    
    /**
     * 发送文件上传请求，自动处理FormData和Content-Type
     * 不会手动设置Content-Type，而是让浏览器根据FormData自动处理
     */
    upload(url: string, formData: FormData, options?: FetchOptions): Promise<Response>;
}

export interface Api {
    request: {
        ai: ApiService;
        main: ApiService;
        agent: ApiService;
        video: ApiService;
        module: ApiService;
        client: ApiService;
    },
    streamFetchRequest: {
        ai: StreamFetchService;
        main: StreamFetchService;
        agent: StreamFetchService;
        video: StreamFetchService;
        module: StreamFetchService;
        client: StreamFetchService;
    };
}

export interface ClinetBaseApiResponse<T = any> {
    code: number;
    message: string;
    data: T;
}
