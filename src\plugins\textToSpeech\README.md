# AI 语音合成页面

一个现代化的语音合成界面，支持暗色和深色模式，具有优雅的UI设计。

## 功能特性

### 🎵 音色选择
- **公共音色**: 提供多种预设的高质量音色
- **我的音色**: 用户自定义的个人音色库
- **音色预览**: 点击播放按钮可以预听音色效果
- **智能推荐**: 根据文本内容推荐最适合的音色

### ⚡ 语音参数调节
- **语速控制**: 0.5x - 2.0x 可调节语速，使用Ant Design滑块组件
- **实时预览**: 滑块调节时实时显示当前语速值和描述
- **标记点**: 在关键语速点显示标记，便于快速选择
- **智能描述**: 根据语速值自动显示"慢速"、"正常"、"快速"
- **主题适配**: 滑块颜色完美适配亮色和暗色主题

### 📝 文本输入
- **智能编辑器**: 支持多行文本输入
- **字符统计**: 实时显示字符数量和限制
- **格式检测**: 自动检测文本格式并优化合成效果

### 🚀 语音合成
- **一键合成**: 点击按钮即可开始语音合成
- **进度显示**: 实时显示合成进度和状态
- **结果预览**: 合成完成后可以预览和下载音频文件

## 界面设计

### 🌈 现代化UI
- **渐变背景**: 使用现代渐变色彩搭配
- **卡片布局**: 清晰的模块化设计
- **图标系统**: 统一的SVG图标设计
- **动画效果**: 流畅的交互动画

### 🌙 主题支持
- **亮色模式**: 清新明亮的界面风格
- **暗色模式**: 护眼的深色界面
- **自动切换**: 跟随系统主题自动切换
- **平滑过渡**: 主题切换时的平滑动画

### 📱 响应式设计
- **移动端适配**: 完美支持手机和平板设备
- **弹性布局**: 自适应不同屏幕尺寸
- **触控优化**: 针对触控设备优化的交互体验

## 技术实现

### 前端技术栈
- **Vue 3**: 使用Composition API
- **TypeScript**: 类型安全的开发体验
- **SCSS**: 模块化的样式管理
- **CSS Variables**: 动态主题切换

### 组件架构
```
VoiceSelection.vue (主组件)
├── 音色选择区域
│   ├── 标签切换 (公共音色/我的音色)
│   └── 音色列表 (网格布局)
├── 语音参数区域
│   └── 语速滑块控制
├── 文本输入区域
│   └── 多行文本编辑器
└── 合成控制区域
    ├── 合成按钮
    └── 结果显示
```

### 状态管理
- **响应式数据**: 使用Vue 3的ref和reactive
- **计算属性**: 自动计算衍生状态
- **监听器**: 响应用户交互和数据变化

## 使用方法

1. **选择音色**
   - 在"公共音色"或"我的音色"标签中选择合适的音色
   - 点击播放按钮预听音色效果

2. **调节参数**
   - 使用语速滑块调节合成语速
   - 观察实时显示的语速值

3. **输入文本**
   - 在文本框中输入要合成的内容
   - 注意字符数量限制

4. **开始合成**
   - 点击"开始合成"按钮
   - 等待合成完成
   - 下载生成的音频文件

## 自定义配置

### 音色数据结构
```typescript
interface Voice {
  id: string;           // 音色唯一标识
  name: string;         // 音色名称
  description: string;  // 音色描述
  gender: 'male' | 'female'; // 性别
  language: string;     // 语言代码
  category: 'public' | 'personal'; // 分类
}
```

### API集成
在 `server/server.ts` 中配置实际的语音合成API：

```typescript
const synthesize = async (params: {
  voiceId: string;
  text: string;
  speed: number;
}) => {
  return await api.request.main.post('tts/synthesize', params);
};
```

## 浏览器兼容性

- ✅ Chrome 88+
- ✅ Firefox 85+
- ✅ Safari 14+
- ✅ Edge 88+

## 开发说明

### 本地开发
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 更新日志

### v1.1.0
- 🔧 **滑块组件优化**: 使用Ant Design滑块组件替换原生滑块
- 🎨 **主题适配**: 滑块颜色完美适配亮色和暗色主题
- 📍 **标记点**: 添加语速标记点，便于快速选择常用语速
- 💬 **智能描述**: 根据语速值自动显示描述文字
- 🎯 **交互优化**: 更好的悬停效果和焦点状态

### v1.0.0
- ✨ 初始版本发布
- 🎨 现代化UI设计
- 🌙 暗色模式支持
- 📱 响应式布局
- 🎵 音色选择功能
- ⚡ 语速调节功能
- 📝 文本输入功能
- 🚀 语音合成功能

## 滑块组件特性

### 🎛️ Ant Design 滑块集成
- **专业组件**: 使用Ant Design的Slider组件，保证稳定性和可访问性
- **主题一致**: 完美融入整体设计风格
- **响应式**: 在不同设备上都有良好的交互体验

### 🎨 视觉设计
- **渐变轨道**: 使用主题色渐变填充，视觉效果更佳
- **圆形手柄**: 18px圆形手柄，带有主题色边框
- **悬停效果**: 手柄悬停时有缩放和阴影效果
- **标记文字**: 关键点位显示标记文字，激活状态高亮

### 🔧 功能特性
- **精确控制**: 0.1步长，精确到小数点后一位
- **实时反馈**: 拖拽时显示tooltip，实时显示当前值
- **智能描述**: 根据数值范围自动显示速度描述
- **键盘支持**: 支持键盘方向键调节
