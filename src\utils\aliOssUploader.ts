// 尝试不同的导入方式并进行兼容处理
import OSS from 'ali-oss';
// 如果上面的导入失败，也可以尝试下面的形式
// import * as OSS_Module from 'ali-oss';
// const OSS = OSS_Module.default || OSS_Module;

// 检查Promise兼容性，防止Q is not a constructor错误
// 这个错误通常发生在Promise实现有问题的环境中
(() => {
  if (typeof Promise === 'undefined') {
    console.error('Promise is not defined in current environment!');
  }
  
  if (typeof Promise !== 'undefined' && 
      typeof Promise.prototype.then !== 'function' ||
      typeof Promise.prototype.catch !== 'function') {
    console.error('Promise implementation seems incomplete or incorrect!');
  }
})();

/**
 * 阿里云OSS上传配置接口
 */
export interface OSSConfig {
  accessKeyId: string;
  accessKeySecret: string;
  stsToken?: string;
  bucket: string;
  region: string;
  endpoint?: string;
  secure?: boolean;
  timeout?: number;
}

/**
 * 上传响应接口
 */
export interface UploadResult {
  url: string;
  name: string;
  success: boolean;
  etag?: string;
  message?: string;
}

/**
 * 上传进度回调函数类型
 */
export type ProgressCallback = (percent: number, checkpoint?: any) => void;

/**
 * 阿里云OSS上传工具类
 */
export class AliOssUploader {
  private client: any = null;
  private config: OSSConfig | null = null;
  
  /**
   * 初始化OSS客户端
   * @param config OSS配置
   */
  initClient(config: OSSConfig): void {
    try {
      console.log('Initializing OSS client with config:', { ...config, accessKeySecret: '***' });
      this.config = config;
      
      const ossConfig: any = {
        accessKeyId: config.accessKeyId,
        accessKeySecret: config.accessKeySecret,
        bucket: config.bucket,
        region: config.region,
        secure: config.secure ?? true,
        timeout: config.timeout ?? 60000
      };
      
      // 如果有STS临时凭证，则添加
      if (config.stsToken) {
        ossConfig.stsToken = config.stsToken;
      }
      
      // 如果有自定义endpoint，则添加
      if (config.endpoint) {
        ossConfig.endpoint = config.endpoint;
      }
      
      console.log('OSS type check:', typeof OSS);
      
      // 使用直接导入的OSS变量
      if (typeof OSS === 'function') {
        console.log('Using OSS as constructor directly');
        try {
          this.client = new OSS(ossConfig);
          console.log('OSS client initialized successfully with direct constructor');
          return;
        } catch (constructorError) {
          console.error('Failed to initialize with direct constructor:', constructorError);
          // 如果直接初始化失败，继续尝试其他方法
        }
      }
      
      // 尝试通过default属性访问
      if (typeof OSS === 'object' && OSS !== null) {
        console.log('OSS is an object, checking for default property');
        const OssConstructor = (OSS as any).default;
        
        if (typeof OssConstructor === 'function') {
          console.log('Using OSS.default as constructor');
          try {
            this.client = new OssConstructor(ossConfig);
            console.log('OSS client initialized successfully with OSS.default');
            return;
          } catch (defaultError) {
            console.error('Failed to initialize with OSS.default:', defaultError);
          }
        }
        
        // 如果是webpack环境，尝试__esModule模式
        if ((OSS as any).__esModule === true) {
          console.log('OSS is an ES module, trying alternative approaches');
          
          for (const key of Object.keys(OSS)) {
            const potentialConstructor = (OSS as any)[key];
            if (typeof potentialConstructor === 'function') {
              console.log(`Trying OSS.${key} as constructor`);
              try {
                this.client = new potentialConstructor(ossConfig);
                console.log(`OSS client initialized successfully with OSS.${key}`);
                return;
              } catch (keyError) {
                console.error(`Failed to initialize with OSS.${key}:`, keyError);
              }
            }
          }
        }
      }
      
      // 如果所有尝试都失败，抛出错误
      console.error('All OSS initialization attempts failed. OSS:', OSS);
      throw new Error('OSS SDK initialization failed: Could not find a valid OSS constructor');
    } catch (error) {
      console.error('Failed to initialize OSS client:', error);
      this.client = null;
      throw error;
    }
  }
  
  /**
   * 获取OSS客户端实例
   */
  private getClient(): any {
    if (!this.client) {
      console.error('OSS client is null. Please call initClient() first.');
      throw new Error('OSS client not initialized. Please call initClient() first.');
    }
    return this.client;
  }
  
  /**
   * 上传文件到OSS
   * @param objectName 对象名称/路径
   * @param file 文件对象
   * @param onProgress 进度回调
   * @returns 上传结果
   */
  async uploadFile(objectName: string, file: File | Blob, onProgress?: ProgressCallback): Promise<UploadResult> {
    try {
      const client = this.getClient();
      const config = this.config!;
      
      // 确保对象名格式正确
      const ossObjectName = objectName.startsWith('/') ? objectName.substring(1) : objectName;
      
      // 执行上传
      const result = await client.put(ossObjectName, file, {
        mime: file instanceof File ? file.type : undefined
      });
      
      if (onProgress) {
        // 上传完成时调用一次进度回调，表示100%
        onProgress(100);
      }
      
      // 构建URL
      const host = config.endpoint || `${config.region}.aliyuncs.com`;
      const protocol = config.secure === false ? 'http' : 'https';
      const url = `${protocol}://${config.bucket}.${host}/${ossObjectName}`;
      
      // 处理etag，使用类型断言访问headers
      const etag = result.res && result.res.headers ? 
        (result.res.headers as Record<string, string>)['etag']?.replace(/"/g, '') || '' : 
        '';
      
      return {
        url,
        name: ossObjectName,
        success: true,
        etag
      };
    } catch (error) {
      console.error('OSS upload error:', error);
      return {
        url: '',
        name: objectName,
        success: false,
        message: error instanceof Error ? error.message : 'Unknown upload error'
      };
    }
  }
  
  /**
   * 分片上传大文件
   * @param objectName 对象名称/路径
   * @param file 文件对象
   * @param onProgress 进度回调
   * @returns 上传结果
   */
  async multipartUpload(objectName: string, file: File | Blob, onProgress?: ProgressCallback): Promise<UploadResult> {
    try {
      const client = this.getClient();
      const config = this.config!;
      
      // 确保对象名格式正确
      const ossObjectName = objectName.startsWith('/') ? objectName.substring(1) : objectName;
      console.log(client);
      // 执行分片上传
      const result = await client.multipartUpload(ossObjectName, file, {
        progress: (p: number, checkpoint: any) => {
          if (onProgress) {
            const percent = Math.floor(p * 100);
            onProgress(percent, checkpoint);
          }
        },
        mime: file instanceof File ? file.type : undefined,
        parallel: 3, // 并行上传分片数
        partSize: 1024 * 1024 // 分片大小，默认1MB
      });
      
      // 构建URL
      const host = config.endpoint || `${config.region}.aliyuncs.com`;
      const protocol = config.secure === false ? 'http' : 'https';
      const url = `${protocol}://${config.bucket}.${host}/${ossObjectName}`;
      
      // 处理etag，使用类型断言访问headers
      const etag = result.res && result.res.headers ? 
        (result.res.headers as Record<string, string>)['etag']?.replace(/"/g, '') || '' : 
        '';
      
      return {
        url,
        name: ossObjectName,
        success: true,
        etag
      };
    } catch (error) {
      console.error('OSS multipart upload error:', error);
      return {
        url: '',
        name: objectName,
        success: false,
        message: error instanceof Error ? error.message : 'Unknown upload error'
      };
    }
  }
  
  /**
   * 获取文件临时访问URL
   * @param objectName 对象名称/路径
   * @param expireSeconds URL过期时间(秒)
   * @returns 临时访问URL
   */
  async getSignedUrl(objectName: string, expireSeconds: number = 3600): Promise<string> {
    try {
      const client = this.getClient();
      const ossObjectName = objectName.startsWith('/') ? objectName.substring(1) : objectName;
      
      const url = await client.signatureUrl(ossObjectName, {
        expires: expireSeconds
      });
      
      return url;
    } catch (error) {
      console.error('Get signed URL error:', error);
      throw error;
    }
  }
  
  /**
   * 检查文件是否存在
   * @param objectName 对象名称/路径
   * @returns 是否存在
   */
  async isObjectExist(objectName: string): Promise<boolean> {
    try {
      const client = this.getClient();
      const ossObjectName = objectName.startsWith('/') ? objectName.substring(1) : objectName;
      
      const result = await client.head(ossObjectName);
      return result.status === 200;
    } catch (error) {
      if ((error as any).code === 'NoSuchKey') {
        return false;
      }
      throw error;
    }
  }
}

// 创建单例实例
export const ossUploader = new AliOssUploader();

// 默认导出单例
export default ossUploader; 