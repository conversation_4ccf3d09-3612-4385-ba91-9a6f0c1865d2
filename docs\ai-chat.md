# AI聊天功能指南

## 概述

系统提供了与AI模型交互的聊天功能，支持流式响应，可以实时获取AI回复。

## 基础使用

```typescript
import {getInject} from "@/utils/getInject";

const {openChat} = getInject();
```

## 初始化客户端

```typescript
// 初始化AI聊天客户端，可选择性传入自定义baseURL
openChat.openAiChatCustom.getClient("https://custom-ai-api.example.com");

// 不传入baseURL则使用默认API地址
openChat.openAiChatCustom.getClient();
```

## 流式聊天

系统支持流式聊天响应，可以实时获取AI回复：

```typescript
// 创建可取消的信号控制器
const controller = new AbortController();
const signal = controller.signal;

// 设置消息接收回调
const onMessage = (chat) => {
    const {choices} = chat;

    if (choices && choices.length > 0) {
        const {delta, finish_reason, index} = choices[0];

        // 获取内容增量
        if (delta.content) {
            console.log('收到内容片段:', delta.content);
            // 更新UI显示
            responseText.value += delta.content;
        }

        // 检查是否结束
        if (finish_reason === 'stop') {
            console.log('聊天完成');
            isLoading.value = false;
        }
    }
};

// 开始流式聊天
try {
    await openChat.openAiChatCustom.startStream(signal, {
        message: "你好，请帮我解释一下这段代码的功能",
        baseURL: "https://ai-api.example.com", // 可选，覆盖初始化时的baseURL
        merge: true // 是否合并响应
    }, onMessage);
} catch (error) {
    console.error('聊天请求出错:', error);
} finally {
    isLoading.value = false;
}

// 需要取消时
const handleCancel = () => {
    controller.abort();
    console.log('用户取消了请求');
};
```

## 响应数据结构

聊天接口返回的数据结构包含以下内容：

```typescript
interface ChunkChat {
    delta: {
        content?: string | null;       // 文本内容
        function_call?: any;           // 函数调用信息
        refusal?: string | null;       // 拒绝回答信息
        role?: 'developer' | 'system' | 'user' | 'assistant' | 'tool'; // 角色
        tool_calls?: Array<any>;       // 工具调用信息
    };
    finish_reason: 'stop' | 'length' | 'tool_calls' | 'content_filter' | 'function_call' | null; // 结束原因
    index: number;                   // 索引
}
```

## Vue组件示例

### 基础聊天组件

```vue
<template>
  <div class="chat-container">
    <div class="messages">
      <div v-for="(msg, index) in messages" :key="index" :class="['message', msg.role]">
        {{ msg.content }}
      </div>
      <div v-if="isLoading" class="message assistant streaming">
        {{ currentResponse }}
        <span class="typing-indicator">...</span>
      </div>
    </div>

    <div class="input-area">
      <input
          v-model="userInput"
          placeholder="输入您的问题..."
          @keyup.enter="sendMessage"
      />
      <button @click="sendMessage" :disabled="isLoading">发送</button>
      <button v-if="isLoading" @click="cancelRequest">取消</button>
    </div>
  </div>
</template>

<script setup>
  import {ref, onUnmounted} from 'vue';
  import {getInject} from "@/utils/getInject";

  const {openChat} = getInject();

  const messages = ref([]);
  const userInput = ref('');
  const currentResponse = ref('');
  const isLoading = ref(false);
  let controller = null;

  // 发送消息
  const sendMessage = async () => {
    if (!userInput.value.trim() || isLoading.value) return;

    // 添加用户消息
    messages.value.push({
      role: 'user',
      content: userInput.value
    });

    const messageText = userInput.value;
    userInput.value = '';
    isLoading.value = true;
    currentResponse.value = '';

    // 创建可取消的控制器
    controller = new AbortController();

    try {
      // 处理AI回复
      const onMessage = (chat) => {
        const {choices} = chat;
        if (choices && choices.length > 0) {
          const {delta} = choices[0];
          if (delta.content) {
            currentResponse.value += delta.content;
          }
        }
      };

      // 发起聊天请求
      await openChat.openAiChatCustom.startStream(controller.signal, {
        message: messageText,
        baseURL: "https://ai-api.example.com",
        merge: true
      }, onMessage);

      // 请求完成，添加AI回复到消息列表
      messages.value.push({
        role: 'assistant',
        content: currentResponse.value
      });

    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('聊天请求失败:', error);
        messages.value.push({
          role: 'system',
          content: '抱歉，请求失败，请稍后再试。'
        });
      }
    } finally {
      isLoading.value = false;
      controller = null;
    }
  };

  // 取消请求
  const cancelRequest = () => {
    if (controller) {
      controller.abort();
      isLoading.value = false;
      messages.value.push({
        role: 'system',
        content: '已取消请求'
      });
    }
  };

  // 组件卸载时清理
  onUnmounted(() => {
    if (controller) {
      controller.abort();
    }
  });
</script>

<style scoped>
  .chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;
  }

  .messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }

  .message {
    margin-bottom: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    max-width: 80%;
  }

  .message.user {
    align-self: flex-end;
    background-color: #e1f5fe;
    margin-left: auto;
  }

  .message.assistant {
    background-color: #f5f5f5;
  }

  .message.system {
    background-color: #fff8e1;
    font-style: italic;
  }

  .typing-indicator {
    display: inline-block;
    animation: typing 1s infinite;
  }

  .input-area {
    display: flex;
    padding: 8px;
    border-top: 1px solid #eee;
  }

  input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 8px;
  }

  button {
    padding: 8px 16px;
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  button:disabled {
    background-color: #b0bec5;
    cursor: not-allowed;
  }

  button:last-child {
    margin-left: 8px;
    background-color: #f44336;
  }

  @keyframes typing {
    0% {
      opacity: 0.3;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.3;
    }
  }
</style>
```

### 高级聊天组件

```vue
<template>
  <div class="advanced-chat">
    <div class="chat-header">
      <h3>AI助手</h3>
      <div class="chat-controls">
        <button @click="clearChat" :disabled="isLoading">清空对话</button>
        <button @click="exportChat" :disabled="!messages.length">导出对话</button>
      </div>
    </div>
    
    <div class="messages-container" ref="messagesContainer">
      <div v-for="(msg, index) in messages" :key="index" class="message-wrapper">
        <div :class="['message', msg.role]">
          <div class="message-header">
            <span class="role">{{ getRoleName(msg.role) }}</span>
            <span class="timestamp">{{ msg.timestamp }}</span>
          </div>
          <div class="message-content" v-html="formatMessage(msg.content)"></div>
        </div>
      </div>
      
      <div v-if="isLoading" class="message-wrapper">
        <div class="message assistant streaming">
          <div class="message-header">
            <span class="role">AI助手</span>
            <span class="status">正在思考...</span>
          </div>
          <div class="message-content">
            {{ currentResponse }}
            <span class="cursor">|</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="input-section">
      <div class="input-tools">
        <button @click="insertTemplate('代码解释')" :disabled="isLoading">代码解释</button>
        <button @click="insertTemplate('翻译')" :disabled="isLoading">翻译</button>
        <button @click="insertTemplate('总结')" :disabled="isLoading">总结</button>
      </div>
      
      <div class="input-area">
        <textarea
          v-model="userInput"
          placeholder="输入您的问题... (Shift+Enter换行，Enter发送)"
          @keydown="handleKeydown"
          rows="3"
        ></textarea>
        <div class="input-actions">
          <button @click="sendMessage" :disabled="isLoading || !userInput.trim()">
            {{ isLoading ? '发送中...' : '发送' }}
          </button>
          <button v-if="isLoading" @click="cancelRequest" class="cancel-btn">
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onUnmounted } from 'vue';
import { getInject } from "@/utils/getInject";

const { openChat } = getInject();

const messages = ref([]);
const userInput = ref('');
const currentResponse = ref('');
const isLoading = ref(false);
const messagesContainer = ref(null);
let controller = null;

// 模板消息
const templates = {
  '代码解释': '请解释以下代码的功能：\n\n',
  '翻译': '请将以下内容翻译成中文：\n\n',
  '总结': '请总结以下内容的要点：\n\n'
};

// 获取角色显示名称
const getRoleName = (role) => {
  const roleNames = {
    'user': '用户',
    'assistant': 'AI助手',
    'system': '系统'
  };
  return roleNames[role] || role;
};

// 格式化消息内容
const formatMessage = (content) => {
  // 简单的markdown渲染
  return content
    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
    .replace(/`([^`]+)`/g, '<code>$1</code>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>');
};

// 插入模板
const insertTemplate = (templateName) => {
  userInput.value = templates[templateName] + userInput.value;
};

// 处理键盘事件
const handleKeydown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
};

// 发送消息
const sendMessage = async () => {
  if (!userInput.value.trim() || isLoading.value) return;

  const messageText = userInput.value.trim();
  const timestamp = new Date().toLocaleTimeString();

  // 添加用户消息
  messages.value.push({
    role: 'user',
    content: messageText,
    timestamp
  });

  userInput.value = '';
  isLoading.value = true;
  currentResponse.value = '';
  scrollToBottom();

  // 创建可取消的控制器
  controller = new AbortController();

  try {
    // 处理AI回复
    const onMessage = (chat) => {
      const { choices } = chat;
      if (choices && choices.length > 0) {
        const { delta, finish_reason } = choices[0];
        
        if (delta.content) {
          currentResponse.value += delta.content;
          scrollToBottom();
        }
        
        if (finish_reason === 'stop') {
          // 添加AI回复到消息列表
          messages.value.push({
            role: 'assistant',
            content: currentResponse.value,
            timestamp: new Date().toLocaleTimeString()
          });
          scrollToBottom();
        }
      }
    };

    // 发起聊天请求
    await openChat.openAiChatCustom.startStream(controller.signal, {
      message: messageText,
      merge: true
    }, onMessage);

  } catch (error) {
    if (error.name !== 'AbortError') {
      console.error('聊天请求失败:', error);
      messages.value.push({
        role: 'system',
        content: '抱歉，请求失败，请稍后再试。',
        timestamp: new Date().toLocaleTimeString()
      });
    }
  } finally {
    isLoading.value = false;
    controller = null;
  }
};

// 取消请求
const cancelRequest = () => {
  if (controller) {
    controller.abort();
    isLoading.value = false;
    messages.value.push({
      role: 'system',
      content: '已取消请求',
      timestamp: new Date().toLocaleTimeString()
    });
  }
};

// 清空对话
const clearChat = () => {
  if (confirm('确定要清空所有对话吗？')) {
    messages.value = [];
    currentResponse.value = '';
  }
};

// 导出对话
const exportChat = () => {
  const chatContent = messages.value
    .map(msg => `[${msg.timestamp}] ${getRoleName(msg.role)}: ${msg.content}`)
    .join('\n\n');
  
  const blob = new Blob([chatContent], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `chat-${new Date().toISOString().slice(0, 10)}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 组件卸载时清理
onUnmounted(() => {
  if (controller) {
    controller.abort();
  }
});
</script>

<style scoped>
.advanced-chat {
  display: flex;
  flex-direction: column;
  height: 600px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.chat-header h3 {
  margin: 0;
  color: #333;
}

.chat-controls {
  display: flex;
  gap: 8px;
}

.chat-controls button {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid #d9d9d9;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
}

.messages-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background-color: #fff;
}

.message-wrapper {
  margin-bottom: 16px;
}

.message {
  max-width: 80%;
  padding: 12px;
  border-radius: 8px;
  position: relative;
}

.message.user {
  margin-left: auto;
  background-color: #1890ff;
  color: white;
}

.message.assistant {
  background-color: #f6f6f6;
  color: #333;
}

.message.system {
  background-color: #fff7e6;
  color: #d46b08;
  text-align: center;
  font-style: italic;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  opacity: 0.7;
}

.message-content {
  line-height: 1.6;
}

.message-content :deep(pre) {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}

.message-content :deep(code) {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Courier New', monospace;
}

.cursor {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.input-section {
  border-top: 1px solid #e8e8e8;
  background-color: #fafafa;
}

.input-tools {
  display: flex;
  gap: 8px;
  padding: 8px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.input-tools button {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid #d9d9d9;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
}

.input-tools button:hover:not(:disabled) {
  background-color: #f0f0f0;
}

.input-area {
  display: flex;
  padding: 16px;
  gap: 12px;
}

textarea {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  resize: vertical;
  font-family: inherit;
}

.input-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.input-actions button:first-child {
  background-color: #1890ff;
  color: white;
}

.input-actions button:first-child:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #ff4d4f;
  color: white;
}
</style>
```

## 配置选项

### 聊天配置参数

```typescript
interface ChatConfig {
  message: string;           // 用户消息内容
  baseURL?: string;         // API基础URL
  merge?: boolean;          // 是否合并响应
  model?: string;           // 使用的模型名称
  temperature?: number;     // 温度参数，控制随机性
  maxTokens?: number;       // 最大token数量
  systemPrompt?: string;    // 系统提示词
}
```

### 使用示例

```typescript
await openChat.openAiChatCustom.startStream(signal, {
  message: "请解释什么是机器学习",
  baseURL: "https://api.openai.com",
  merge: true,
  model: "izdax",
  temperature: 0.7,
  maxTokens: 1000,
  systemPrompt: "你是一个专业的AI助手，请用简洁明了的语言回答问题。"
}, onMessage);
```

## 错误处理

### 常见错误类型

```typescript
// 网络错误
if (error.name === 'NetworkError') {
  console.log('网络连接失败');
}

// 请求被取消
if (error.name === 'AbortError') {
  console.log('请求被用户取消');
}
```

### 错误处理最佳实践

```typescript
const handleChatError = (error) => {
  let errorMessage = '请求失败，请稍后再试';
  
  if (error.name === 'AbortError') {
    return; // 用户主动取消，不显示错误
  }
  
  
};
```

## 最佳实践

1. **请求管理**：使用AbortController管理请求生命周期
2. **错误处理**：完善的错误处理和用户提示
3. **用户体验**：提供清晰的加载状态和进度反馈
4. **性能优化**：合理设置请求参数，避免过长响应
5. **数据安全**：注意API密钥的安全存储
6. **内容过滤**：对AI响应内容进行适当的过滤和格式化

## 注意事项

> **内容审核**：对用户输入和AI输出进行适当的内容审核和过滤。