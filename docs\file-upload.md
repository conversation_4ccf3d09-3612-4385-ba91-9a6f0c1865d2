# 文件上传指南

## OSS文件上传

系统提供了通过依赖注入的OSS上传工具，方便实现文件上传功能：

```typescript
import {getInject} from "@/utils/getInject";

const {ossUtils} = getInject();
```

## 初始化OSS客户端

在应用启动时或模块加载时进行初始化：

```typescript
const initOssUploader = async () => {
    try {
        // 初始化OSS客户端，传入必要的配置信息
        await ossUtils.initClient({
            accessKeyId: 'your-access-key-id',
            accessKeySecret: 'your-access-key-secret',
            bucket: 'your-bucket-name',
            region: 'oss-cn-hangzhou', // 例如：oss-cn-hangzhou
            secure: true, // 使用HTTPS，默认为true
            timeout: 60000, // 超时时间，默认60秒

            // 可选参数
            stsToken: 'temporary-security-token', // STS临时凭证，如果使用STS授权
            endpoint: 'custom.endpoint.com' // 自定义endpoint，如果需要
        });
        console.log('OSS客户端初始化成功');
        return true;
    } catch (error) {
        console.error('OSS客户端初始化失败:', error);
        return false;
    }
};
```

## 文件上传方法

### 基础文件上传

```typescript
const uploadFile = async (file: File) => {
    try {
        // 上传文件，参数1：存储路径，参数2：文件对象，参数3：进度回调
        const result = await ossUtils.uploadFile(
            `uploads/${Date.now()}-${file.name}`, 
            file, 
            (percent) => {
                console.log(`上传进度: ${percent}%`);
                // 更新进度条UI
            }
        );

        if (result.success) {
            console.log('上传成功，文件URL:', result.url);
            return result.url;
        } else {
            throw new Error('上传失败');
        }
    } catch (error) {
        console.error('文件上传错误:', error);
        throw error;
    }
};
```

### 分片上传大文件

```typescript
const uploadLargeFile = async (file: File) => {
    try {
        const result = await ossUtils.multipartUpload(
            `large-files/${Date.now()}-${file.name}`,
            file,
            (percent) => {
                console.log(`大文件上传进度: ${percent}%`);
            }
        );
        
        return result.url;
    } catch (error) {
        console.error('大文件上传失败:', error);
        throw error;
    }
};
```

## Vue组件示例

### 基础上传组件

```vue
<template>
  <div class="upload-container">
    <div v-if="!ossInitialized">
      <button @click="initOss">初始化OSS</button>
    </div>
    <div v-else>
      <input type="file" @change="handleFileChange"/>
      <button @click="startUpload" :disabled="isUploading || !selectedFile">
        {{ isUploading ? `上传中 ${uploadProgress}%` : '开始上传' }}
      </button>
      <div v-if="fileUrl" class="result">
        上传成功: <a :href="fileUrl" target="_blank">{{ fileUrl }}</a>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {ref, onMounted} from 'vue';
  import {getInject} from "@/utils/getInject";

  const {ossUtils} = getInject();

  const selectedFile = ref(null);
  const isUploading = ref(false);
  const uploadProgress = ref(0);
  const fileUrl = ref('');
  const ossInitialized = ref(false);

  // 初始化OSS客户端
  const initOss = async () => {
    try {
      // 可以从环境变量、配置文件或API获取这些配置
      await ossUtils.initClient({
        accessKeyId: 'your-access-key-id',
        accessKeySecret: 'your-access-key-secret',
        bucket: 'your-bucket-name',
        region: 'oss-cn-hangzhou',
        secure: true
      });
      ossInitialized.value = true;
      console.log('OSS初始化成功');
    } catch (error) {
      console.error('OSS初始化失败:', error);
      alert('OSS初始化失败，请检查配置');
    }
  };

  const handleFileChange = (event) => {
    selectedFile.value = event.target.files[0] || null;
    fileUrl.value = '';
  };

  const startUpload = async () => {
    if (!selectedFile.value || !ossInitialized.value) return;

    isUploading.value = true;
    uploadProgress.value = 0;

    try {
      // 使用注入的ossUtils进行上传
      const result = await ossUtils.uploadFile(
          `user-uploads/${Date.now()}-${selectedFile.value.name}`,
          selectedFile.value,
          (percent) => {
            uploadProgress.value = percent;
          }
      );

      if (result.url) {
        fileUrl.value = result.url;
      }
    } catch (error) {
      console.error('上传失败:', error);
      alert('文件上传失败，请重试');
    } finally {
      isUploading.value = false;
    }
  };
</script>

<style scoped>
.upload-container {
  padding: 20px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  text-align: center;
}

.result {
  margin-top: 16px;
  padding: 8px;
  background-color: #f0f9ff;
  border-radius: 4px;
}

button {
  margin: 8px;
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}
</style>
```

### 拖拽上传组件

```vue
<template>
  <div 
    class="drag-upload"
    :class="{ 'drag-over': isDragOver }"
    @drop="handleDrop"
    @dragover="handleDragOver"
    @dragleave="handleDragLeave"
  >
    <div v-if="!fileList.length" class="upload-placeholder">
      <p>拖拽文件到此处或点击选择文件</p>
      <input type="file" multiple @change="handleFileSelect" style="display: none" ref="fileInput">
      <button @click="$refs.fileInput.click()">选择文件</button>
    </div>
    
    <div v-else class="file-list">
      <div v-for="(file, index) in fileList" :key="index" class="file-item">
        <span>{{ file.name }}</span>
        <div class="progress-bar">
          <div class="progress" :style="{ width: file.progress + '%' }"></div>
        </div>
        <span>{{ file.status }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { getInject } from "@/utils/getInject";

const { ossUtils } = getInject();

const isDragOver = ref(false);
const fileList = ref([]);

const handleDragOver = (e) => {
  e.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = () => {
  isDragOver.value = false;
};

const handleDrop = (e) => {
  e.preventDefault();
  isDragOver.value = false;
  
  const files = Array.from(e.dataTransfer.files);
  processFiles(files);
};

const handleFileSelect = (e) => {
  const files = Array.from(e.target.files);
  processFiles(files);
};

const processFiles = (files) => {
  files.forEach(file => {
    const fileItem = {
      name: file.name,
      progress: 0,
      status: '准备上传',
      file: file
    };
    
    fileList.value.push(fileItem);
    uploadFile(fileItem);
  });
};

const uploadFile = async (fileItem) => {
  try {
    fileItem.status = '上传中';
    
    const result = await ossUtils.uploadFile(
      `uploads/${Date.now()}-${fileItem.file.name}`,
      fileItem.file,
      (percent) => {
        fileItem.progress = percent;
      }
    );
    
    fileItem.status = '上传成功';
    fileItem.url = result.url;
  } catch (error) {
    fileItem.status = '上传失败';
    console.error('上传失败:', error);
  }
};
</script>

<style scoped>
.drag-upload {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  transition: border-color 0.3s;
}

.drag-upload.drag-over {
  border-color: #1890ff;
  background-color: #f0f9ff;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  margin: 0 16px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #1890ff;
  transition: width 0.3s;
}
</style>
```

## 文件上传API

ossUtils提供了以下方法用于文件操作：

### 上传相关方法

```typescript
// 上传普通文件
const result = await ossUtils.uploadFile(objectKey, file, progressCallback);

// 分片上传大文件
const result = await ossUtils.multipartUpload(objectKey, largeFile, progressCallback);

// 获取文件的临时访问URL
const url = await ossUtils.getSignedUrl(objectKey, expireSeconds);
```

### 文件信息获取

```typescript
// 获取文件信息
const fileInfo = await ossUtils.getObjectMeta(objectKey);

// 列出指定前缀的文件
const fileList = await ossUtils.listObjects({
  prefix: 'uploads/',
  maxKeys: 100
});
```

## 配置选项

### 初始化配置

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `accessKeyId` | string | 是 | 访问密钥ID |
| `accessKeySecret` | string | 是 | 访问密钥Secret |
| `bucket` | string | 是 | 存储桶名称 |
| `region` | string | 是 | 地域，如oss-cn-hangzhou |
| `secure` | boolean | 否 | 是否使用HTTPS，默认true |
| `timeout` | number | 否 | 超时时间，默认60000ms |
| `stsToken` | string | 否 | STS临时凭证 |
| `endpoint` | string | 否 | 自定义endpoint |


## 最佳实践

1. **文件命名**：使用时间戳等方式确保文件名唯一性
2. **路径规划**：合理规划文件存储路径结构
3. **权限控制**：根据业务需求设置合适的文件访问权限
4. **错误处理**：完善的错误处理和用户提示
5. **进度显示**：为用户提供清晰的上传进度反馈
6. **文件校验**：上传前进行文件类型和大小校验
7. **安全考虑**：不要在前端暴露敏感的访问密钥信息

## 注意事项

> **重要提示**：objectKey 是文件在OSS中的存储路径，建议使用时间戳等方式确保文件名唯一性，避免同名文件覆盖问题。

> **安全提醒**：在生产环境中，建议使用STS临时凭证而不是长期的AccessKey，以提高安全性。