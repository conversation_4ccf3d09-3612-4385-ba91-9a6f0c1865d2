# izdaxAi 插件开发指南 (V3.1)

## 一、插件开发流程

1. **创建插件**

```bash
npm run create-plugin [插件名称]
# 示例
npm run create-plugin demo-plugin
```

生成目录结构：

```
src/plugins/[插件名]/
  ├── index.ts    # 插件入口文件
  ├── index.vue   # 插件主组件
  ├── router.ts   # 路由配置
  ├── components/ # 子组件目录
  ├── server/     # API自定义封装目录
  │   └── server.ts # API服务文件  
  ├── view/       # 多路由视图目录
  └── assets/     # 静态资源目录
```

2. **构建插件**

```bash
npm run run-build [插件名称]
# 构建产物输出至 dist/[插件名]/
```

3. **部署流程**
   将 `dist/[插件名]` 目录上传至管理系统后台

## 二、核心工具库

### 1. MSTF-Kit 工具集

gitee官方提供 [mstf-kit](https://gitee.com/xsid/mstf-kit)
npm网站 [mstf-kit](https://www.npmjs.com/package/mstf-kit)完整工具函数，包含：

- 数据校验
- 日期处理
- 字符串操作
- 加密解密
- 类型转换等
- 音频处理
- 音频流式处理

使用示例：

```typescript
import {formatDate, deepClone} from 'mstf-kit';

// 日期格式化
const today = formatDate(new Date(), 'YYYY-MM-DD');

// 深拷贝
const dataCopy = deepClone(originalData);
```

### 2. 依赖注入工具

```typescript
import {getInject} from "@/utils/getInject";

const {
    api,         // API请求实例
    router,      // 路由实例  
    userStore,   // 用户状态
    ossUtils,    // OSS上传工具
    utilStore,   // 工具状态
    globalUtils, // 全局工具
    openChat,    // 聊天模块
    getEnv       // 环境变量
} = getInject();
```

## 三、常用指令集

| 指令              | 功能说明          | 示例用法                               |
|-----------------|---------------|------------------------------------|
| v-ms-auth       | 登录态校验拦截       | `<button v-ms-auth>操作</button>`    |
| v-debounce      | 输入防抖（默认300ms） | `<input v-debounce:500="handler">` |
| v-ms-auth-input | 输入框登录态监听      | `<input v-ms-auth-input>`          |
| v-ms-copy       | 文本复制功能        | `<span v-ms-copy="'要复制的文本'">`      |

## 四、API请求封装

详细的API请求封装指南请参考：[API请求封装指南](./docs/api-requests.md)

## 五、路由配置

详细的路由配置指南请参考：[路由配置指南](./docs/router-config.md)

## 六、文件上传

详细的文件上传指南请参考：[文件上传指南](./docs/file-upload.md)

## 七、状态管理

详细的状态管理使用指南请参考：[utilStore 使用指南](./docs/util-store.md)

```typescript
// 用户信息获取
const {userInfo, token} = userStore || {};

// 工具状态
const {deviceType, themeMode} = utilStore || {};
```

## 八、环境判断

```typescript
const env = getEnv(); // 'development' | 'production'
```

## 十一、主题颜色配置

详细的主题颜色配置指南请参考：[主题颜色配置指南](./docs/colors.md)

## 九、语音处理功能

详细的语音处理功能指南请参考：[语音处理功能指南](./docs/voice-processing.md)

## 十、AI聊天功能

详细的AI聊天功能指南请参考：[AI聊天功能指南](./docs/ai-chat.md)

---## 
📚 文档导航

### 核心功能文档
- [API请求封装指南](./docs/api-requests.md) - 详细的API请求和流式请求使用指南
- [路由配置指南](./docs/router-config.md) - 插件路由配置和管理
- [文件上传指南](./docs/file-upload.md) - OSS文件上传功能详解
- [语音处理功能指南](./docs/voice-processing.md) - 语音识别和VAD功能
- [AI聊天功能指南](./docs/ai-chat.md) - AI聊天和流式对话功能

### 设计规范文档
- [主题颜色配置指南](./docs/colors.md) - 完整的主题颜色系统和使用规范

### 快速链接
| 功能模块 | 文档链接 | 描述 |
|---------|---------|------|
| API请求 | [api-requests.md](./docs/api-requests.md) | 基础API、流式请求、文件上传API |
| 路由配置 | [router-config.md](./docs/router-config.md) | 路由注册、守卫、嵌套路由 |
| 文件上传 | [file-upload.md](./docs/file-upload.md) | OSS上传、拖拽上传、进度控制 |
| 语音处理 | [voice-processing.md](./docs/voice-processing.md) | VAD检测、语音转文字 |
| AI聊天 | [ai-chat.md](./docs/ai-chat.md) | 流式聊天、消息管理 |
| 主题颜色 | [colors.md](./docs/colors.md) | 颜色变量、主题切换 |