<script setup lang="ts">
import {ref, onMounted, nextTick} from 'vue';
import {Modal as AModal} from 'ant-design-vue';
import {useProvideInject} from '@/utils/routerHelper';  // 引入封装的 provide/inject 函数

// 创建响应式数据
const api = ref(null);
const userStore = ref(null);
const utilStore = ref(null);
const router = ref(null);
const ossUtils = ref(null);
const openAiChatCustom = ref(null);
const getEnv = ref(null);
const globalUtils = ref(null);
const isDataReady = ref(false);
const isModalVisible = ref(false); // 控制 Modal 显示

// 获取 iframe 和相关的 API 数据
const iframe = ref<HTMLIFrameElement | null>(null);

// 添加主题相关逻辑
const isDarkMode = ref(false)

// 切换主题函数
const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value
  document.documentElement.classList.toggle('dark')
  // 保存主题设置到本地存储
  localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light')
}

// Modal 关闭时刷新页面
const handleModalClose = () => {
  // 刷新页面
  location.reload();
};

onMounted(() => {
  nextTick(() => {
    if (iframe.value) {
      iframe.value.onload = () => {
        const iframeWindow: any = iframe.value?.contentWindow;

        // 检查 iframe 中的 token 或登录状态
        if (iframeWindow.userStore.userInfo.access_token) {
          // 如果有 token，赋值给 userStore
          userStore.value = iframeWindow.userStore;
          api.value = iframeWindow.api;
          router.value = iframeWindow.router;
          ossUtils.value = iframeWindow.ossUtils;
          utilStore.value = iframeWindow.utilStore;
          globalUtils.value = iframeWindow.globalUtils;
          openAiChatCustom.value = iframeWindow.openAiChatCustom;
          openAiChatCustom.value = iframeWindow.openAiChatCustom;
          getEnv.value = iframeWindow.getEnv;
          isDataReady.value = true;
        } else {
          // 如果没有 token，显示登录弹窗
          isModalVisible.value = true;
        }
      };
    }
  });
  window.addEventListener('storage', handleStorageChange);

  // 从本地存储获取主题设置
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme === 'dark') {
    isDarkMode.value = true
    document.documentElement.classList.add('dark')
  }
});

// 使用封装函数提供数据，提供的数据会自动解包
useProvideInject("api", api, true);
useProvideInject("userStore", userStore, true);
useProvideInject("router", router, true);
useProvideInject("ossUtils", ossUtils, true);
useProvideInject("utilStore", utilStore, true);
useProvideInject("globalUtils", globalUtils, true);
useProvideInject("openAiChatCustom", openAiChatCustom, true);
useProvideInject("getEnv", getEnv, true);

// 监听 storage 事件
const handleStorageChange = (event: StorageEvent) => {
  if (event.key === 'd-i-a-w') {
    // 如果 key 值是 'd-i-a-w'，更新响应式变量
    if (event.newValue) {
      const newData = JSON.parse(event.newValue)
      if (!newData.userInfo.access_token) {
        isModalVisible.value = true;
      } else {
        isModalVisible.value = false;
        handleModalClose();
      }
    }
  }
};
</script>

<template>
  <a-modal
      v-model:open="isModalVisible"
      title="请登录"
      :footer="null"
      width="800px"
      :style="{ height: '600px' }"
      :maskClosable="false"
      @cancel="handleModalClose"
  >
    <iframe
        src="/debug/?token=15A6717A17D81BCD7B97819E45184505"
        style="width: 100%; height: 500px; border: none;"
    ></iframe>
  </a-modal>

  <iframe ref="iframe" src="/debug/?token=15A6717A17D81BCD7B97819E45184505&time=1231231239989090909" style="display: none"></iframe>

  <!-- 只有在数据准备好之后，才显示主视图 -->
  <router-view v-if="isDataReady"></router-view>

  <!-- 添加主题切换悬浮球 -->
  <div class="theme-toggle" @click="toggleTheme" :class="{ 'dark': isDarkMode }">
    <!-- 亮色主题图标 -->
    <svg v-if="!isDarkMode" class="icon" viewBox="0 0 24 24" width="24" height="24">
      <path
          d="M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zM2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1zm18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1zM11 2v2c0 .55.45 1 1 1s1-.45 1-1V2c0-.55-.45-1-1-1s-1 .45-1 1zm0 18v2c0 .55.45 1 1 1s1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1zM5.99 4.58c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0s.39-1.03 0-1.41L5.99 4.58zm12.37 12.37c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0 .39-.39.39-1.03 0-1.41l-1.06-1.06zm1.06-10.96c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0l1.06-1.06zM7.05 18.36c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0l1.06-1.06z"
          fill="currentColor"
      />
    </svg>
    <!-- 暗色主题图标 -->
    <svg v-else class="icon" viewBox="0 0 24 24" width="24" height="24">
      <path
          d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9c0-.46-.04-.92-.1-1.36-.98 1.37-2.58 2.26-4.4 2.26-3.03 0-5.5-2.47-5.5-5.5 0-1.82.89-3.42 2.26-4.4-.44-.06-.9-.1-1.36-.1z"
          fill="currentColor"
      />
    </svg>
  </div>
</template>

<style scoped>
.theme-toggle {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--bg-color);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 1000;
  color: var(--text-color-secondary);
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.theme-toggle.dark {
  background: var(--component-bg);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

.theme-toggle .icon {
  transition: all 0.3s;
}

.theme-toggle:hover .icon {
  color: var(--primary-color);
}

/* 维吾尔语 RTL 支持 */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

/* 输入框和文本区域 */
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] [contenteditable="true"] {
  direction: rtl;
  text-align: right;
}

/* Ant Design Vue 组件 RTL 支持 */
[dir="rtl"] .ant-input,
[dir="rtl"] .ant-select,
[dir="rtl"] .ant-dropdown,
[dir="rtl"] .ant-modal,
[dir="rtl"] .ant-form {
  direction: rtl;
  text-align: right;
}

/* 按钮组 */
[dir="rtl"] .ant-btn-group {
  flex-direction: row-reverse;
}

/* 表单项 */
[dir="rtl"] .ant-form-item-label {
  text-align: right;
}
</style>
