# 语音处理功能指南

## 语音活动检测 (VAD)

系统提供了语音活动检测功能，可以自动识别用户的语音输入开始和结束。

## 基础使用

```typescript
import {getInject} from "@/utils/getInject";

const {globalUtils} = getInject();
```

## 初始化语音识别

```typescript
// 初始化语音识别
globalUtils.vad.initVad({
    onSpeechStart: () => {
        console.log('检测到语音开始');
        // 可以在这里显示录音状态
    },
    onSpeechEnd: (audioData) => {
        console.log('语音结束，获取到音频数据');
        // 处理音频数据
        processAudioData(audioData);
    },
    onVadReadyChange: (isReady) => {
        console.log('VAD状态变化:', isReady ? '已就绪' : '未就绪');
        // 更新UI状态
    }
});
```

## 控制方法

```typescript
// 开始语音检测
globalUtils.vad.startVad();

// 停止语音检测
globalUtils.vad.stopVad();

// 手动触发语音结束(例如按钮控制的场景)
globalUtils.vad.triggerSpeechEnd();
```

## Vue组件示例

### 基础语音录制组件

```vue
<template>
  <div class="voice-recorder">
    <div class="status-indicator" :class="{ active: isRecording, ready: isVadReady }">
      <div class="pulse" v-if="isRecording"></div>
      <span>{{ statusText }}</span>
    </div>
    
    <div class="controls">
      <button @click="initializeVad" :disabled="isVadReady">
        初始化语音识别
      </button>
      <button @click="startRecording" :disabled="!isVadReady || isRecording">
        开始录音
      </button>
      <button @click="stopRecording" :disabled="!isRecording">
        停止录音
      </button>
      <button @click="manualEnd" :disabled="!isRecording">
        手动结束
      </button>
    </div>
    
    <div v-if="audioUrl" class="audio-result">
      <h3>录音结果:</h3>
      <audio :src="audioUrl" controls></audio>
      <button @click="downloadAudio">下载音频</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue';
import { getInject } from "@/utils/getInject";

const { globalUtils } = getInject();

const isVadReady = ref(false);
const isRecording = ref(false);
const audioUrl = ref('');

const statusText = computed(() => {
  if (!isVadReady.value) return '未初始化';
  if (isRecording.value) return '正在录音...';
  return '准备就绪';
});

// 初始化VAD
const initializeVad = async () => {
  try {
    await globalUtils.vad.initVad({
      onSpeechStart: () => {
        console.log('检测到语音开始');
        isRecording.value = true;
      },
      onSpeechEnd: (audioData) => {
        console.log('语音结束，获取到音频数据');
        isRecording.value = false;
        processAudioData(audioData);
      },
      onVadReadyChange: (ready) => {
        console.log('VAD状态变化:', ready);
        isVadReady.value = ready;
      }
    });
  } catch (error) {
    console.error('VAD初始化失败:', error);
    alert('语音识别初始化失败，请检查麦克风权限');
  }
};

// 开始录音
const startRecording = () => {
  if (!isVadReady.value) return;
  
  globalUtils.vad.startVad();
  console.log('开始语音检测');
};

// 停止录音
const stopRecording = () => {
  globalUtils.vad.stopVad();
  isRecording.value = false;
  console.log('停止语音检测');
};

// 手动结束录音
const manualEnd = () => {
  globalUtils.vad.triggerSpeechEnd();
  console.log('手动触发语音结束');
};

// 处理音频数据
const processAudioData = (audioData) => {
  try {
    // 创建音频URL用于播放
    const blob = new Blob([audioData], { type: 'audio/wav' });
    audioUrl.value = URL.createObjectURL(blob);
    
    console.log('音频数据处理完成，大小:', audioData.byteLength);
    
    // 这里可以将音频数据发送到服务器进行语音识别
    // sendAudioToServer(audioData);
  } catch (error) {
    console.error('音频数据处理失败:', error);
  }
};

// 下载音频文件
const downloadAudio = () => {
  if (!audioUrl.value) return;
  
  const a = document.createElement('a');
  a.href = audioUrl.value;
  a.download = `recording-${Date.now()}.wav`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

// 组件卸载时清理
onUnmounted(() => {
  if (isRecording.value) {
    globalUtils.vad.stopVad();
  }
  if (audioUrl.value) {
    URL.revokeObjectURL(audioUrl.value);
  }
});
</script>

<style scoped>
.voice-recorder {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.status-indicator {
  text-align: center;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  background-color: #f5f5f5;
  position: relative;
}

.status-indicator.ready {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-indicator.active {
  background-color: #fff2e8;
  color: #fa8c16;
}

.pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background-color: #ff4d4f;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 20px;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #1890ff;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
}

button:hover:not(:disabled) {
  background-color: #40a9ff;
}

button:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

.audio-result {
  text-align: center;
  padding: 20px;
  background-color: #f6ffed;
  border-radius: 8px;
}

.audio-result h3 {
  margin-top: 0;
  color: #52c41a;
}

audio {
  width: 100%;
  margin: 10px 0;
}
</style>
```

### 语音转文字组件

```vue
<template>
  <div class="speech-to-text">
    <div class="recording-area">
      <button 
        @click="toggleRecording" 
        :class="['record-btn', { recording: isRecording }]"
        :disabled="!isVadReady"
      >
        {{ isRecording ? '停止录音' : '开始录音' }}
      </button>
      
      <div v-if="isRecording" class="recording-indicator">
        <div class="wave"></div>
        <span>正在录音...</span>
      </div>
    </div>
    
    <div class="transcription-result">
      <h3>识别结果:</h3>
      <div class="text-output" v-if="transcriptionText">
        {{ transcriptionText }}
      </div>
      <div v-else class="placeholder">
        录音完成后，识别结果将显示在这里
      </div>
    </div>
    
    <div class="history" v-if="transcriptionHistory.length">
      <h3>历史记录:</h3>
      <div class="history-item" v-for="(item, index) in transcriptionHistory" :key="index">
        <span class="timestamp">{{ item.timestamp }}</span>
        <span class="text">{{ item.text }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { getInject } from "@/utils/getInject";

const { globalUtils } = getInject();

const isVadReady = ref(false);
const isRecording = ref(false);
const transcriptionText = ref('');
const transcriptionHistory = ref([]);

// 初始化VAD
onMounted(async () => {
  try {
    await globalUtils.vad.initVad({
      onSpeechStart: () => {
        console.log('开始录音');
        isRecording.value = true;
        transcriptionText.value = '';
      },
      onSpeechEnd: async (audioData) => {
        console.log('录音结束');
        isRecording.value = false;
        
        // 发送音频数据进行语音识别
        await processAudioForTranscription(audioData);
      },
      onVadReadyChange: (ready) => {
        isVadReady.value = ready;
      }
    });
  } catch (error) {
    console.error('VAD初始化失败:', error);
  }
});

// 切换录音状态
const toggleRecording = () => {
  if (isRecording.value) {
    globalUtils.vad.stopVad();
  } else {
    globalUtils.vad.startVad();
  }
};

// 处理音频进行语音识别
const processAudioForTranscription = async (audioData) => {
  try {
    // 这里应该调用语音识别API
    // 示例：调用百度、阿里云、腾讯云等语音识别服务
    const text = await callSpeechRecognitionAPI(audioData);
    
    transcriptionText.value = text;
    
    // 添加到历史记录
    transcriptionHistory.value.unshift({
      timestamp: new Date().toLocaleTimeString(),
      text: text
    });
    
    // 限制历史记录数量
    if (transcriptionHistory.value.length > 10) {
      transcriptionHistory.value = transcriptionHistory.value.slice(0, 10);
    }
    
  } catch (error) {
    console.error('语音识别失败:', error);
    transcriptionText.value = '识别失败，请重试';
  }
};

// 调用语音识别API（示例）
const callSpeechRecognitionAPI = async (audioData) => {
  // 这里是示例代码，实际需要根据使用的语音识别服务进行调整
  const formData = new FormData();
  const audioBlob = new Blob([audioData], { type: 'audio/wav' });
  formData.append('audio', audioBlob);
  
  const response = await fetch('/api/speech-recognition', {
    method: 'POST',
    body: formData
  });
  
  const result = await response.json();
  return result.text || '识别失败';
};

// 清理资源
onUnmounted(() => {
  if (isRecording.value) {
    globalUtils.vad.stopVad();
  }
});
</script>

<style scoped>
.speech-to-text {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.recording-area {
  text-align: center;
  margin-bottom: 30px;
}

.record-btn {
  padding: 15px 30px;
  font-size: 16px;
  border: none;
  border-radius: 50px;
  background-color: #1890ff;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
}

.record-btn.recording {
  background-color: #ff4d4f;
  animation: pulse-btn 1.5s infinite;
}

.record-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

@keyframes pulse-btn {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.recording-indicator {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.wave {
  width: 20px;
  height: 20px;
  background-color: #ff4d4f;
  border-radius: 50%;
  animation: wave 1s infinite;
}

@keyframes wave {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

.transcription-result {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.text-output {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  min-height: 60px;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.placeholder {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.history {
  background-color: #fafafa;
  padding: 20px;
  border-radius: 8px;
}

.history-item {
  display: flex;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.timestamp {
  color: #999;
  font-size: 12px;
  min-width: 80px;
}

.text {
  flex: 1;
  color: #333;
}
</style>
```

## 配置选项

### VAD初始化配置

```typescript
interface VadConfig {
  // 语音开始回调
  onSpeechStart?: () => void;
  
  // 语音结束回调，返回音频数据
  onSpeechEnd?: (audioData: ArrayBuffer) => void;
  
  // VAD状态变化回调
  onVadReadyChange?: (isReady: boolean) => void;
  
  // 音频采样率，默认16000
  sampleRate?: number;
  
  // 静音检测阈值，默认0.01
  silenceThreshold?: number;
  
  // 静音持续时间（毫秒），超过此时间认为语音结束，默认1500
  silenceDuration?: number;
  
  // 最小录音时长（毫秒），默认500
  minRecordingTime?: number;
  
  // 最大录音时长（毫秒），默认30000
  maxRecordingTime?: number;
}
```

### 使用示例

```typescript
await globalUtils.vad.initVad({
  onSpeechStart: () => {
    console.log('开始录音');
  },
  onSpeechEnd: (audioData) => {
    console.log('录音结束，音频大小:', audioData.byteLength);
  },
  onVadReadyChange: (isReady) => {
    console.log('VAD状态:', isReady);
  },
  sampleRate: 16000,
  silenceThreshold: 0.01,
  silenceDuration: 2000,
  minRecordingTime: 1000,
  maxRecordingTime: 60000
});
```

## 音频数据处理

### 音频格式转换

```typescript
// 将ArrayBuffer转换为Blob
const audioBlob = new Blob([audioData], { type: 'audio/wav' });

// 创建音频URL用于播放
const audioUrl = URL.createObjectURL(audioBlob);

// 创建FormData用于上传
const formData = new FormData();
formData.append('audio', audioBlob, 'recording.wav');
```

### 音频质量优化

```typescript
// 音频降噪处理（需要额外的音频处理库）
const processAudio = (audioData) => {
  // 使用Web Audio API进行音频处理
  const audioContext = new AudioContext();
  
  return audioContext.decodeAudioData(audioData.buffer)
    .then(audioBuffer => {
      // 进行降噪、增益等处理
      return processedAudioBuffer;
    });
};
```

## 最佳实践

1. **权限处理**：确保用户授权麦克风权限
2. **错误处理**：完善的错误处理和用户提示
3. **性能优化**：合理设置录音参数，避免过长录音
4. **用户体验**：提供清晰的录音状态反馈
5. **数据安全**：注意音频数据的隐私保护
6. **兼容性**：考虑不同浏览器的兼容性问题

## 注意事项

> **权限要求**：语音功能需要用户授权麦克风权限，请确保在HTTPS环境下使用。

> **浏览器兼容性**：部分功能可能在某些浏览器中不支持，建议进行兼容性检测。

> **音频格式**：默认输出WAV格式音频，如需其他格式请进行相应转换。