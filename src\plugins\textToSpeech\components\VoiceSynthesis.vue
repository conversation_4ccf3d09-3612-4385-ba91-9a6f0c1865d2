<template>
    <div class="voice-synthesis">
        <div class="speed-conf">
            <div class="speed-title">
                语速调节
            </div>
            <div class="speed-slider">
                <div class="slider-value">{{ speed }}X</div>
                <div class="slider">
                    <span>慢</span>
                    <div class="slider-box">
                        <a-slider v-model:value="speed" :min="0.5" :max="2" :step="0.1" />
                    </div>
                    <span>快</span>
                </div>
            </div>
        </div>
        <div class="speek-content">
            <div class="speek-con-top">
                <div class="speek-con-title">
                    输入文本
                </div>
                <div class="speek-con-lang">
                    {{ currentLang }}
                </div>
                <div class="speek-con-voice">
                    {{ currentVoiceName?'已选：':'未选' }}
                    <span :dir="textDir(currentVoiceName)">{{currentVoiceName}}</span>
                </div>
            </div>
            <div class="text-con">
                <a-input v-model:value="target_text" :bordered="false" :maxlength="2500" placeholder="请输入合成文本"
                    class="text-area" :dir="textAreaDir"></a-input>
                <div class="text-count">{{ target_text?.length || 0 }}/2500</div>
            </div>
            <div class="speek-controller">
                <div class="clone-btn" @click="onSynthesis" :disabled="isLoading">
                    <Button :type="isLoading ? 'loading' : 'voice'" :content="isLoading ? '合成中...' : '开始合成'"></Button>
                </div>
                <div class="audio-player" v-if="audioUrl">
                    <AudioPlayer :audioUrl="audioUrl" />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted, watch } from 'vue' // 导入ref函数
import { Slider as aSlider, Textarea as aInput, message } from 'ant-design-vue'
import {detectLanguage,textDir} from '../utils/utils'
import { textToSpeechSerApi } from '../server/server'
import AudioPlayer from './AudioPlayer.vue'
import Button from './Button.vue';
const textToSpeechApi = textToSpeechSerApi()
const speed = ref<number>(1)
const target_text = ref<string>();

// 音频播放相关状态
const isLoading = ref<boolean>(false)
const audioUrl = ref<string>('')

const props = defineProps({
    currentVoice: {
        type: Object,
        default: 0
    }
})
const currentVoiceName = ref<string>('')
const textAreaDir = ref<string | undefined>('ltr')
const currentLang = ref<string>('')

watch(() => props.currentVoice, (newVoice) => {

    currentVoiceName.value = newVoice.name
})
const onSynthesis = async () => {
    if (!target_text.value?.trim()) {
        message.error('请输入要合成的文本')
        return
    }

    isLoading.value = true

    try {
        const response: any = await textToSpeechApi.synthesize({
            clone_id: props.currentVoice.id,
            text: target_text.value ?? '',
            speed: speed.value,
        })
        console.log(response);

        if (response.code == 200) {
            audioUrl.value = response.url
            message.success('语音合成成功')
        } else {
            message.error(response.msg || '语音合成失败')
        }

    } catch (error) {
        console.error('语音合成失败:', error)
        message.error('语音合成失败，请重试')
    } finally {
        isLoading.value = false
    }
}


watch(target_text, (newText) => {
    const { text, dir } = detectLanguage(newText);
    currentLang.value = text; // 改变文本框的语言
    textAreaDir.value = dir; // 改变文本框的方向
});

// 组件卸载时清理资源
onUnmounted(() => {
    // 现在audioUrl存储的是服务器返回的URL，不需要手动清理
    audioUrl.value = ''
})
</script>
<style lang="scss">
.voice-synthesis {
    width: 60%;
    margin: 0 auto;
    padding: 24px;
    border: 1px solid var(--border-color);
    border-radius: 24px;
    background: var(--card-bg);
    margin-top: 20px;

    .speed-conf {
        font-size: 18px;
        color: var(--text-color);
        padding-bottom: 5px;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 15px;

        .speed-title {
            margin-bottom: 20px;
        }

        .speed-slider {
            padding: 0 10px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;

            .slider-value {
                background: var(--primary-color);
                margin-right: 20px;
                height: 24px;
                width: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 18px;
                font-size: 14px;
                color: white;
            }

            .slider {
                width: 90%;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .slider-box {
                    width: 95%;
                }

                span {
                    font-size: 14px;
                    color: var(--text-color);
                }
            }
        }
    }

    .speek-content {
        .speek-con-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-color);
            margin-top: 20px;
        }

        .text-con {
            margin-bottom: 10px;
            position: relative;

            .text-count {
                position: absolute;
                right: 20px;
                bottom: 20px;
                color: var(--text-color);
                font-size: 12px;
                background: white;
                padding: 6px 10px;
                border-radius: 22px;
                background: color-mix(in srgb, var(--card-bg), transparent 30%);
            }

            .text-area {
                resize: none;
                height: 300px;
                background: var(--textarea-bg);
                border-radius: 18px;
                margin-top: 20px;
                padding: 20px;
                font-size: 14px;
                color: var(--text-color);
            }

            .text-area::-webkit-scrollbar {
                width: 6px;
                height: 6px;
                padding: 100px 0;
            }

            .text-area::-webkit-scrollbar-track {
                background: transparent;
            }

            .text-area::-webkit-scrollbar-thumb {
                background: #0000001a;
                border-radius: 3px;
            }

            .loading {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 18px;
                backdrop-filter: blur(2px);
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 8px;
                /* 添加元素间距 */

                &>div {
                    height: 48px;
                    position: relative;
                    animation: icon_ani 1.5s linear infinite;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }


                /* 更平滑的延迟分布 */
                @for $i from 1 through 10 {
                    &>div:nth-child(#{$i}) {
                        animation-delay: $i * 0.12s;
                    }
                }

                @keyframes icon_ani {

                    0% {
                        transform: translateY(0px)
                    }

                    25% {
                        transform: translateY(8px)
                    }

                    50% {
                        transform: translateY(16px)
                    }

                    75% {
                        transform: translateY(0px)
                    }

                    100% {
                        transform: translateY(0px)
                    }
                }
            }
        }

        .speek-controller {
            display: flex;
            .clone-btn{
                width: 180px;
            }
            .audio-player {
                width: calc(100% - 200px);

                // width: 80%;
                .audio-player {
                    width: 80%;
                    margin: 0 auto;
                }
            }
        }
    }
}

@media screen and (max-width: 1500px) {
    .voice-synthesis {
        width: 100%;
        padding: 15px;

        .speek-controller {
            display: flex;
            margin-top: 20px;
            flex-direction: column-reverse;

            .clone-btn {
                width: 100% !important;
                margin-top: 20px;
            }

            .audio-player {
                width: 100% !important;
            }
        }
    }
}
</style>