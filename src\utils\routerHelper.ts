import {inject, provide, ref, type Ref} from 'vue';
import type {Router} from "vue-router";
import {getInject} from "@/utils/getInject.ts";

// 动态导入 router，确保只在开发环境使用
let router: Router;
if (import.meta.env.MODE === 'development') {
    import("@/router").then((routerModule) => {
        router = routerModule.default;
    });
}

/**
 * 自定义封装的 provide/inject 函数，自动处理数据提供和注入。
 * @param key - provide/inject 的 key
 * @param data - 默认提供的数据
 * @param isProvide - 是否是通过 provide 提供数据，外层传递该值
 * @returns - 返回解包后的数据，外层无需使用 .value
 */
export function useProvideInject<T>(key: string, data: T | null = null, isProvide: boolean = false): T {
    const isDevProvide = import.meta.env.MODE === 'development';
    if (isProvide) {
        // 如果 isProvide 为 true，使用 provide 提供数据
        const providedData = ref(data) as Ref<T>;
        provide(key, providedData);
        return providedData.value;  // 返回解包后的响应式数据
    } else {
        // 如果 isProvide 为 false，使用 inject 获取数据
        const injectedData = inject(key, data) as Ref<T> | T;
        if (!injectedData) {
            throw new Error(`Injecting '${key}' failed`);
        }
        return isDevProvide ? (injectedData as Ref<T>).value : (injectedData as T);  // 解包响应式数据，或者直接返回原始数据
    }
}

/**
 * 返回 router，开发环境使用动态加载，生产环境使用 provide 注入。
 */
export function routers() {
    if (import.meta.env.MODE === 'development') {
        return router;
    } else {
        return getInject().router;
    }
}
