import {createRouter, createWebHistory} from 'vue-router'
import type {RouteRecordRaw} from 'vue-router'

// 基础路由配置
const routes: RouteRecordRaw[] = [
    {
        path: '/',
        name: 'Welcome',
        component: () => import('@/plugins/index.vue'),
        meta: {
            title: "欢迎页"
        }
    }
]

// 创建路由实例
const systemRouter = createRouter({
    history: createWebHistory(),
    routes
})

// 自动导入所有插件路由
const modules = import.meta.glob('@/plugins/**/router.ts', {
    eager: true,
    import: 'default'
})

// 处理并添加插件路由
Object.values(modules).forEach((moduleRoutes: any) => {
    if (Array.isArray(moduleRoutes)) {
        // 如果是路由数组，直接添加
        moduleRoutes.forEach(route => {
            systemRouter.addRoute(route)
        })
    } else if (moduleRoutes.routes) {
        // 如果是路由实例，添加其路由配置
        moduleRoutes.routes.forEach((route: RouteRecordRaw) => {
            systemRouter.addRoute(route)
        })
    }
})

export default systemRouter