# utilStore 使用指南

## 一、基础使用

utilStore 是基于 Pinia 状态管理库构建的工具状态管理器，提供了设备类型、主题模式等常用状态管理功能。

```typescript
import {getInject} from "@/utils/getInject";

const {utilStore} = getInject();

// 获取基础状态
const {deviceType, themeMode} = utilStore || {};
```

## 二、自定义变量管理

utilStore 提供了两个核心方法用于管理自定义变量：

### setCustomVariable - 设置自定义变量

用于设置任意类型的自定义变量到状态管理中。

```typescript
// 方法签名
setCustomVariable: (key: string, value: any) => void;
```

**使用示例：**

```typescript
import {getInject} from "@/utils/getInject";

const {utilStore} = getInject();

// 设置字符串类型变量
utilStore.setCustomVariable('test-name', 'adasd');

// 设置数字类型变量
utilStore.setCustomVariable('test-number', 25);

// 设置布尔类型变量
utilStore.setCustomVariable('test-bool', true);

// 设置对象类型变量
utilStore.setCustomVariable('test-object', {
    id: 123,
    name: 'John Doe',
    email: '<EMAIL>',
    preferences: {
        theme: 'dark',
        language: 'zh-CN'
    }
});

// 设置数组类型变量
utilStore.setCustomVariable('userTags', ['developer', 'vue', 'typescript']);

```

### getCustomVariable - 获取自定义变量

用于从状态管理中获取之前设置的自定义变量。

```typescript
// 方法签名
getCustomVariable: (key: string) => any;
```

**使用示例：**

```typescript
import {getInject} from "@/utils/getInject";

const {utilStore} = getInject();

// 获取字符串变量
const userName = utilStore.getCustomVariable('test-name');
console.log('用户名:', userName); // 输出: 用户名: John Doe

// 获取不存在的变量（返回 undefined）
const nonExistent = utilStore.getCustomVariable('nonExistentKey');
console.log('不存在的变量:', nonExistent); // 输出: 不存在的变量: undefined
```

## 三、实际应用场景

### 场景1：用户偏好设置管理

```typescript
const {utilStore} = getInject();

// 设置用户偏好
const setUserPreferences = (preferences) => {
    utilStore.setCustomVariable('userPreferences', {
        ...utilStore.getCustomVariable('userPreferences') || {},
        ...preferences
    });
};

// 获取用户偏好
const getUserPreference = (key) => {
    const preferences = utilStore.getCustomVariable('userPreferences') || {};
    return preferences[key];
};

```

### 场景2：临时状态管理

```typescript
const {utilStore} = getInject();

// 设置临时加载状态
const setLoadingState = (key, isLoading) => {
    const loadingStates = utilStore.getCustomVariable('loadingStates') || {};
    loadingStates[key] = isLoading;
    utilStore.setCustomVariable('loadingStates', loadingStates);
};

// 获取加载状态
const getLoadingState = (key) => {
    const loadingStates = utilStore.getCustomVariable('loadingStates') || {};
    return loadingStates[key] || false;
};

// 使用示例
setLoadingState('userProfile', true);
setLoadingState('projectList', true);

// 在组件中使用
const isUserProfileLoading = getLoadingState('userProfile');
const isProjectListLoading = getLoadingState('projectList');
```

## 四、注意事项

1. **类型安全**: 由于 value 参数类型为 any，建议在 TypeScript 项目中进行类型断言：

```typescript
// 类型安全的获取方式
interface UserProfile {
    id: number;
    name: string;
    email: string;
}

const userProfile = utilStore.getCustomVariable('userProfile') as UserProfile;
```

2. **命名规范**: 建议使用有意义的 key 名称，避免冲突：

```typescript
// 推荐的命名方式
utilStore.setCustomVariable('plugin_demo_config', config);
utilStore.setCustomVariable('user_current_project', projectId);
utilStore.setCustomVariable('ui_sidebar_collapsed', false);
```

3. **性能考虑**: 避免存储过大的对象或频繁更新，这可能影响应用性能。

## 五、Pinia 状态管理原理

utilStore 基于 Pinia 构建，Pinia 是 Vue 3 推荐的状态管理库：

- **响应式**: 状态变化会自动触发相关组件的重新渲染
- **类型安全**: 提供完整的 TypeScript 支持
- **开发工具**: 支持 Vue DevTools 调试
- **模块化**: 支持多个 store 实例

通过 setCustomVariable 和 getCustomVariable 方法，你可以在不了解 Pinia 复杂 API 的情况下，轻松管理应用状态。