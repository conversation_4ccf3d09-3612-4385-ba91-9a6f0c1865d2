<script setup lang="ts">
import {routers} from "@/utils/routerHelper.ts";
import {ref, onMounted} from 'vue';

// 根据环境获取 router 实例
const router = routers()

interface RouteGroup {
  modulePath: string;
  routes: any[];
}

const routeGroups = ref<RouteGroup[]>([]);

onMounted(async () => {
  // 获取所有路由模块
  const modules = import.meta.glob('@/plugins/**/router.ts', {
    eager: true,
    import: 'default'
  });

  // 处理路由分组
  const groups: RouteGroup[] = [];
  
  Object.entries(modules).forEach(([modulePath, moduleRoutes]: [string, any]) => {
    const routes = Array.isArray(moduleRoutes) 
      ? moduleRoutes 
      : moduleRoutes.routes || [];
      
    // 从路径中提取插件名称
    const pluginName = modulePath.split('/')[3]; // src/plugins/[pluginName]/router.ts
    
    groups.push({
      modulePath: pluginName || '默认路由',
      routes
    });
  });

  routeGroups.value = groups;
});
</script>

<template>
  <div class="welcome">
    <div class="welcome-header">
      <div class="logo">
        <img src="/logo.svg" alt="logo" />
      </div>
      <h1>izdaxAi 插件管理系统</h1>
      <p class="subtitle">便捷、高效的插件开发与管理平台</p>
      <div class="header-decoration"></div>
    </div>

    <div class="routes-container">
      <div v-for="group in routeGroups" 
           :key="group.modulePath" 
           class="route-group">
        <div class="group-header">
          <h2>{{ group.modulePath }} 模块</h2>
          <span class="route-count">{{ group.routes.length }} 个路由</span>
        </div>
        <div class="routes-grid">
          <div v-for="route in group.routes" 
               :key="route.path" 
               class="route-card"
               @click="router.push(route.path)">
            <div class="route-icon">
              <i class="route-default-icon"></i>
            </div>
            <div class="route-content">
              <div class="route-title">{{ route.meta?.title || route.name }}</div>
              <div class="route-path">{{ route.path }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.welcome {
  min-height: 100vh;
  padding: 40px;
  background: linear-gradient(to bottom, #f0f2f5, #fff);
}

.welcome-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  padding: 40px 0;
}

.logo {
  margin-bottom: 24px;
}

.logo img {
  width: 80px;
  height: 80px;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

.header-decoration {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #40a9ff, #1890ff);
  border-radius: 2px;
}

h1 {
  font-size: 42px;
  color: #1a1a1a;
  margin-bottom: 16px;
  font-weight: 600;
  background: linear-gradient(120deg, #1890ff, #40a9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  font-size: 20px;
  color: #666;
  margin-bottom: 40px;
}

.routes-container {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.route-group {
  margin-bottom: 48px;
}

.route-group:last-child {
  margin-bottom: 0;
}

.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

h2 {
  font-size: 24px;
  color: #1a1a1a;
  font-weight: 500;
  padding-left: 16px;
  border-left: 4px solid #1890ff;
  margin-right: 16px;
}

.route-count {
  color: #8c8c8c;
  font-size: 14px;
  background: #f5f5f5;
  padding: 4px 12px;
  border-radius: 12px;
}

.routes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.route-card {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f0f0f0;
}

.route-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.06);
  border-color: #1890ff;
  background: linear-gradient(to right, #fff, #f7f9ff);
}

.route-icon {
  width: 40px;
  height: 40px;
  background: #f0f7ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.route-default-icon {
  width: 20px;
  height: 20px;
  background: #1890ff;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z'/%3E%3C/svg%3E") no-repeat center;
}

.route-content {
  flex: 1;
}

.route-title {
  font-size: 16px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 6px;
}

.route-path {
  font-size: 13px;
  color: #8c8c8c;
  font-family: 'Fira Code', monospace;
}

@media (max-width: 768px) {
  .welcome {
    padding: 20px;
  }
  
  .routes-container {
    padding: 24px;
  }
  
  .routes-grid {
    grid-template-columns: 1fr;
  }
  
  h1 {
    font-size: 32px;
  }
  
  .subtitle {
    font-size: 16px;
  }
}
</style>