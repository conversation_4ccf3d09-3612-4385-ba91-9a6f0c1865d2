<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 800" width="800" height="800">
  <defs>
    <!-- 左侧渐变：从紫色到蓝色 -->
    <linearGradient id="leftGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#7C3AED" />
      <stop offset="100%" stop-color="#2563EB" />
    </linearGradient>
    <!-- 右侧渐变：从青色到深蓝 -->
    <linearGradient id="rightGradient" x1="1" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#0EA5E9" />
      <stop offset="100%" stop-color="#1D4ED8" />
    </linearGradient>
  </defs>

  <!-- 左侧弧形结构 -->
  <path
          d="M240 400 Q240 240 400 240"
          fill="none"
          stroke="url(#leftGradient)"
          stroke-width="32"
          stroke-linecap="round"
  />
  <path
          d="M240 400 Q240 560 400 560"
          fill="none"
          stroke="url(#leftGradient)"
          stroke-width="32"
          stroke-linecap="round"
  />

  <!-- 右侧弧形结构 -->
  <path
          d="M560 400 Q560 240 400 240"
          fill="none"
          stroke="url(#rightGradient)"
          stroke-width="32"
          stroke-linecap="round"
  />
  <path
          d="M560 400 Q560 560 400 560"
          fill="none"
          stroke="url(#rightGradient)"
          stroke-width="32"
          stroke-linecap="round"
  />

  <!-- 中心圆点 -->
  <circle cx="400" cy="400" r="40" fill="url(#rightGradient)" />
</svg>
